import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import type { SLDLCliOptions, DownloadJob, DownloadStatus } from '../types/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export interface SLDLCliProgress {
  jobId: string;
  status: DownloadStatus;
  progress: number;
  currentTrack?: string;
  message?: string;
  error?: string;
}

export class SLDLCliService extends EventEmitter {
  private activeProcesses = new Map<string, ChildProcess>();
  private sldlCliPath: string;
  private tempDir: string;

  constructor() {
    super();
    this.sldlCliPath = process.env.SLDL_CLI_PATH || 'slsk-batchdl';
    this.tempDir = process.env.TEMP_DIR || path.join(__dirname, '../../temp');
    this.ensureTempDir();
  }

  private async ensureTempDir(): Promise<void> {
    try {
      await fs.ensureDir(this.tempDir);
    } catch (error) {
      console.error('Failed to create temp directory:', error);
    }
  }

  async executeDownload(job: DownloadJob, options: SLDLCliOptions): Promise<void> {
    const jobId = job.id;
    console.log(`Starting SLDL CLI execution for job ${jobId}`);

    try {
      // Create temporary input file for the track
      const inputFile = await this.createInputFile(job, options);
      
      // Build SLDL CLI arguments
      const args = this.buildCliArguments(inputFile, options);
      
      console.log(`Executing SLDL CLI: ${this.sldlCliPath} ${args.join(' ')}`);

      // Spawn SLDL CLI process
      const process = spawn(this.sldlCliPath, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: options.musicDir
      });

      this.activeProcesses.set(jobId, process);

      // Handle process output
      this.setupProcessHandlers(jobId, process, job);

      // Handle process completion
      process.on('close', (code) => {
        this.activeProcesses.delete(jobId);
        this.handleProcessCompletion(jobId, code, job);
      });

      process.on('error', (error) => {
        console.error(`SLDL CLI process error for job ${jobId}:`, error);
        this.activeProcesses.delete(jobId);
        this.emit('progress', {
          jobId,
          status: DownloadStatus.FAILED,
          progress: 0,
          error: `Process error: ${error.message}`
        } as SLDLCliProgress);
      });

    } catch (error) {
      console.error(`Failed to start SLDL CLI for job ${jobId}:`, error);
      this.emit('progress', {
        jobId,
        status: DownloadStatus.FAILED,
        progress: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      } as SLDLCliProgress);
    }
  }

  async cancelDownload(jobId: string): Promise<void> {
    const process = this.activeProcesses.get(jobId);
    if (process) {
      console.log(`Cancelling SLDL CLI process for job ${jobId}`);
      process.kill('SIGTERM');
      this.activeProcesses.delete(jobId);
    }
  }

  private async createInputFile(job: DownloadJob, options: SLDLCliOptions): Promise<string> {
    const inputFileName = `input-${job.id}.txt`;
    const inputFilePath = path.join(this.tempDir, inputFileName);

    // Create input content based on track information
    const trackLine = `${job.track.artists.join(', ')} - ${job.track.name}`;
    
    await fs.writeFile(inputFilePath, trackLine + '\n', 'utf8');
    
    return inputFilePath;
  }

  private buildCliArguments(inputFile: string, options: SLDLCliOptions): string[] {
    const args: string[] = [];

    // Input file
    args.push('--input', inputFile);

    // Music directory
    args.push('--music-dir', options.musicDir);

    // Soulseek credentials
    args.push('--username', options.username);
    args.push('--password', options.password);

    // Concurrent downloads
    args.push('--concurrent', options.concurrent.toString());

    // Listen port
    args.push('--port', options.port.toString());

    // Audio formats
    if (options.formats.length > 0) {
      args.push('--format', options.formats.join(','));
    }

    // Minimum bitrate
    if (options.minBitrate > 0) {
      args.push('--min-bitrate', options.minBitrate.toString());
    }

    // Search options
    if (options.removeFt) {
      args.push('--remove-ft');
    }

    if (options.customRegex) {
      args.push('--regex', options.customRegex);
    }

    if (options.artistMaybeWrong) {
      args.push('--artist-maybe-wrong');
    }

    if (options.desperate) {
      args.push('--desperate');
    }

    // yt-dlp fallback
    if (options.ytDlp) {
      args.push('--yt-dlp');
    }

    // Max retries
    args.push('--max-retries', options.maxRetries.toString());

    // Additional options for better integration
    args.push('--no-modify-playlist'); // Don't modify input file
    args.push('--print-results'); // Print results for parsing
    args.push('--print-progress'); // Print progress for parsing

    return args;
  }

  private setupProcessHandlers(jobId: string, process: ChildProcess, job: DownloadJob): void {
    let outputBuffer = '';
    let errorBuffer = '';

    // Handle stdout
    process.stdout?.on('data', (data: Buffer) => {
      const output = data.toString();
      outputBuffer += output;
      
      // Parse output for progress information
      this.parseOutput(jobId, output, job);
    });

    // Handle stderr
    process.stderr?.on('data', (data: Buffer) => {
      const error = data.toString();
      errorBuffer += error;
      
      // Parse error output
      this.parseErrorOutput(jobId, error, job);
    });

    // Store buffers for debugging
    process.on('close', () => {
      if (outputBuffer.trim()) {
        console.log(`SLDL CLI output for job ${jobId}:`, outputBuffer);
      }
      if (errorBuffer.trim()) {
        console.log(`SLDL CLI errors for job ${jobId}:`, errorBuffer);
      }
    });
  }

  private parseOutput(jobId: string, output: string, job: DownloadJob): void {
    const lines = output.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      // Parse different types of SLDL output
      if (trimmedLine.includes('Searching for')) {
        this.emit('progress', {
          jobId,
          status: DownloadStatus.SEARCHING,
          progress: 10,
          currentTrack: job.track.name,
          message: trimmedLine
        } as SLDLCliProgress);
      }
      else if (trimmedLine.includes('Downloading')) {
        this.emit('progress', {
          jobId,
          status: DownloadStatus.DOWNLOADING,
          progress: 50,
          currentTrack: job.track.name,
          message: trimmedLine
        } as SLDLCliProgress);
      }
      else if (trimmedLine.includes('Downloaded')) {
        this.emit('progress', {
          jobId,
          status: DownloadStatus.COMPLETED,
          progress: 100,
          currentTrack: job.track.name,
          message: trimmedLine
        } as SLDLCliProgress);
      }
      else if (trimmedLine.includes('Failed') || trimmedLine.includes('Error')) {
        this.emit('progress', {
          jobId,
          status: DownloadStatus.FAILED,
          progress: 0,
          currentTrack: job.track.name,
          error: trimmedLine
        } as SLDLCliProgress);
      }
      else if (trimmedLine.includes('yt-dlp')) {
        this.emit('progress', {
          jobId,
          status: DownloadStatus.YTDLP_FALLBACK,
          progress: 25,
          currentTrack: job.track.name,
          message: trimmedLine
        } as SLDLCliProgress);
      }
      
      // Parse progress percentages if available
      const progressMatch = trimmedLine.match(/(\d+)%/);
      if (progressMatch) {
        const progress = parseInt(progressMatch[1]);
        this.emit('progress', {
          jobId,
          status: DownloadStatus.DOWNLOADING,
          progress,
          currentTrack: job.track.name,
          message: trimmedLine
        } as SLDLCliProgress);
      }
    }
  }

  private parseErrorOutput(jobId: string, error: string, job: DownloadJob): void {
    const lines = error.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      // Log errors but don't necessarily fail the job
      console.warn(`SLDL CLI warning/error for job ${jobId}:`, trimmedLine);
      
      // Only emit error status for critical errors
      if (trimmedLine.toLowerCase().includes('fatal') || 
          trimmedLine.toLowerCase().includes('critical')) {
        this.emit('progress', {
          jobId,
          status: DownloadStatus.FAILED,
          progress: 0,
          currentTrack: job.track.name,
          error: trimmedLine
        } as SLDLCliProgress);
      }
    }
  }

  private handleProcessCompletion(jobId: string, exitCode: number | null, job: DownloadJob): void {
    console.log(`SLDL CLI process for job ${jobId} completed with exit code: ${exitCode}`);

    if (exitCode === 0) {
      // Success
      this.emit('progress', {
        jobId,
        status: DownloadStatus.COMPLETED,
        progress: 100,
        currentTrack: job.track.name,
        message: 'Download completed successfully'
      } as SLDLCliProgress);
    } else {
      // Failure
      this.emit('progress', {
        jobId,
        status: DownloadStatus.FAILED,
        progress: 0,
        currentTrack: job.track.name,
        error: `Process exited with code ${exitCode}`
      } as SLDLCliProgress);
    }

    // Clean up temporary input file
    this.cleanupTempFiles(jobId);
  }

  private async cleanupTempFiles(jobId: string): Promise<void> {
    try {
      const inputFile = path.join(this.tempDir, `input-${jobId}.txt`);
      if (await fs.pathExists(inputFile)) {
        await fs.remove(inputFile);
      }
    } catch (error) {
      console.warn(`Failed to cleanup temp files for job ${jobId}:`, error);
    }
  }

  // Check if SLDL CLI is available
  async checkSLDLAvailability(): Promise<{ available: boolean; version?: string; error?: string }> {
    return new Promise((resolve) => {
      const process = spawn(this.sldlCliPath, ['--version'], { stdio: 'pipe' });
      
      let output = '';
      let error = '';

      process.stdout?.on('data', (data) => {
        output += data.toString();
      });

      process.stderr?.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve({
            available: true,
            version: output.trim()
          });
        } else {
          resolve({
            available: false,
            error: error.trim() || `Exit code: ${code}`
          });
        }
      });

      process.on('error', (err) => {
        resolve({
          available: false,
          error: err.message
        });
      });

      // Timeout after 5 seconds
      setTimeout(() => {
        process.kill();
        resolve({
          available: false,
          error: 'Timeout - SLDL CLI not responding'
        });
      }, 5000);
    });
  }

  // Cleanup method
  cleanup(): void {
    console.log('Cleaning up SLDLCliService');
    
    // Kill all active processes
    for (const [jobId, process] of this.activeProcesses.entries()) {
      console.log(`Killing SLDL CLI process for job ${jobId}`);
      process.kill('SIGTERM');
    }
    
    this.activeProcesses.clear();
    this.removeAllListeners();
  }
}
