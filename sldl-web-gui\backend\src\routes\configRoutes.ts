import { Router } from 'express';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import type { ApiResponse, DownloadConfig } from '../types/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration file path
const CONFIG_FILE = path.join(__dirname, '../../config/user-config.json');

export const configRoutes = Router();

// Get current configuration
configRoutes.get('/', async (req, res) => {
  try {
    // Ensure config directory exists
    await fs.ensureDir(path.dirname(CONFIG_FILE));

    let config: Partial<DownloadConfig> = {};

    // Try to load existing config
    if (await fs.pathExists(CONFIG_FILE)) {
      const configData = await fs.readJson(CONFIG_FILE);
      config = configData;
    }

    // Remove sensitive data from response
    const safeConfig = {
      ...config,
      spotifyClientSecret: config.spotifyClientSecret ? '***hidden***' : '',
      soulseekPassword: config.soulseekPassword ? '***hidden***' : ''
    };

    res.json({
      success: true,
      data: safeConfig
    } as ApiResponse);

  } catch (error) {
    console.error('Get config error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get configuration'
    } as ApiResponse);
  }
});

// Save configuration
configRoutes.post('/', async (req, res) => {
  try {
    const config = req.body as DownloadConfig;

    // Validate required fields
    const requiredFields = [
      'spotifyClientId', 'spotifyClientSecret',
      'soulseekUsername', 'soulseekPassword',
      'downloadDirectory'
    ];

    for (const field of requiredFields) {
      if (!config[field as keyof DownloadConfig]) {
        return res.status(400).json({
          success: false,
          error: `Missing required field: ${field}`
        } as ApiResponse);
      }
    }

    // Ensure config directory exists
    await fs.ensureDir(path.dirname(CONFIG_FILE));

    // Load existing config to preserve any fields not in the request
    let existingConfig: Partial<DownloadConfig> = {};
    if (await fs.pathExists(CONFIG_FILE)) {
      existingConfig = await fs.readJson(CONFIG_FILE);
    }

    // Merge with existing config
    const mergedConfig = {
      ...existingConfig,
      ...config,
      updatedAt: new Date().toISOString()
    };

    // Save configuration
    await fs.writeJson(CONFIG_FILE, mergedConfig, { spaces: 2 });

    // Return safe config (without sensitive data)
    const safeConfig = {
      ...mergedConfig,
      spotifyClientSecret: '***hidden***',
      soulseekPassword: '***hidden***'
    };

    res.json({
      success: true,
      data: safeConfig,
      message: 'Configuration saved successfully'
    } as ApiResponse);

  } catch (error) {
    console.error('Save config error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to save configuration'
    } as ApiResponse);
  }
});

// Test Spotify credentials
configRoutes.post('/test-spotify', async (req, res) => {
  try {
    const { spotifyClientId, spotifyClientSecret } = req.body;

    if (!spotifyClientId || !spotifyClientSecret) {
      return res.status(400).json({
        success: false,
        error: 'Missing Spotify credentials'
      } as ApiResponse);
    }

    // Test Spotify API credentials
    const response = await fetch('https://accounts.spotify.com/api/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${spotifyClientId}:${spotifyClientSecret}`).toString('base64')}`
      },
      body: 'grant_type=client_credentials'
    });

    if (response.ok) {
      res.json({
        success: true,
        message: 'Spotify credentials are valid'
      } as ApiResponse);
    } else {
      res.status(400).json({
        success: false,
        error: 'Invalid Spotify credentials'
      } as ApiResponse);
    }

  } catch (error) {
    console.error('Test Spotify credentials error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to test Spotify credentials'
    } as ApiResponse);
  }
});

// Test Soulseek connection (placeholder - would need actual SLDL CLI integration)
configRoutes.post('/test-soulseek', async (req, res) => {
  try {
    const { soulseekUsername, soulseekPassword } = req.body;

    if (!soulseekUsername || !soulseekPassword) {
      return res.status(400).json({
        success: false,
        error: 'Missing Soulseek credentials'
      } as ApiResponse);
    }

    // TODO: Implement actual Soulseek connection test via SLDL CLI
    // For now, just validate that credentials are provided
    res.json({
      success: true,
      message: 'Soulseek credentials received (connection test not yet implemented)'
    } as ApiResponse);

  } catch (error) {
    console.error('Test Soulseek credentials error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to test Soulseek credentials'
    } as ApiResponse);
  }
});

// Validate download directory
configRoutes.post('/validate-directory', async (req, res) => {
  try {
    const { directory } = req.body;

    if (!directory) {
      return res.status(400).json({
        success: false,
        error: 'Directory path is required'
      } as ApiResponse);
    }

    // Check if directory exists and is writable
    try {
      await fs.ensureDir(directory);
      
      // Test write access
      const testFile = path.join(directory, '.sldl-test-write');
      await fs.writeFile(testFile, 'test');
      await fs.remove(testFile);

      res.json({
        success: true,
        message: 'Directory is valid and writable'
      } as ApiResponse);

    } catch (dirError) {
      res.status(400).json({
        success: false,
        error: `Directory is not accessible: ${dirError instanceof Error ? dirError.message : 'Unknown error'}`
      } as ApiResponse);
    }

  } catch (error) {
    console.error('Validate directory error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to validate directory'
    } as ApiResponse);
  }
});
