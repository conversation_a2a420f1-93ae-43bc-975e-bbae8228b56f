import { Router } from 'express';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs-extra';
import path from 'path';
import type { DownloadManager } from '../services/DownloadManager.js';
import type { ApiResponse } from '../types/index.js';

const execAsync = promisify(exec);

export function statusRoutes(downloadManager: DownloadManager): Router {
  const router = Router();

  // Get overall system status
  router.get('/', async (req, res) => {
    try {
      const status = {
        server: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.env.npm_package_version || '1.0.0',
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch
        },
        downloads: {
          active: await downloadManager.getActiveDownloadCount(),
          queued: await downloadManager.getQueuedDownloadCount(),
          total: await downloadManager.getTotalDownloadCount()
        },
        sldl: {
          available: false,
          version: null,
          path: null
        }
      };

      // Check if SLDL CLI is available
      try {
        const sldlPath = process.env.SLDL_CLI_PATH || 'slsk-batchdl';
        const { stdout } = await execAsync(`"${sldlPath}" --version`);
        status.sldl.available = true;
        status.sldl.version = stdout.trim();
        status.sldl.path = sldlPath;
      } catch (error) {
        // SLDL CLI not available or not in PATH
        status.sldl.available = false;
      }

      res.json({
        success: true,
        data: status
      } as ApiResponse);

    } catch (error) {
      console.error('Get status error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get status'
      } as ApiResponse);
    }
  });

  // Get download statistics
  router.get('/stats', async (req, res) => {
    try {
      const stats = await downloadManager.getDownloadStats();

      res.json({
        success: true,
        data: stats
      } as ApiResponse);

    } catch (error) {
      console.error('Get stats error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get statistics'
      } as ApiResponse);
    }
  });

  // Check disk space for download directory
  router.post('/disk-space', async (req, res) => {
    try {
      const { directory } = req.body;

      if (!directory) {
        return res.status(400).json({
          success: false,
          error: 'Directory path is required'
        } as ApiResponse);
      }

      // Check if directory exists
      if (!await fs.pathExists(directory)) {
        return res.status(400).json({
          success: false,
          error: 'Directory does not exist'
        } as ApiResponse);
      }

      // Get disk space information (platform-specific)
      let diskSpace;
      try {
        if (process.platform === 'win32') {
          const { stdout } = await execAsync(`dir "${directory}" /-c`);
          const lines = stdout.split('\n');
          const lastLine = lines[lines.length - 2];
          const match = lastLine.match(/(\d+) bytes free/);
          if (match) {
            diskSpace = {
              free: parseInt(match[1]),
              total: null, // Not easily available from dir command
              used: null
            };
          }
        } else {
          const { stdout } = await execAsync(`df -B1 "${directory}"`);
          const lines = stdout.split('\n');
          const dataLine = lines[1];
          const parts = dataLine.split(/\s+/);
          diskSpace = {
            total: parseInt(parts[1]),
            used: parseInt(parts[2]),
            free: parseInt(parts[3])
          };
        }
      } catch (error) {
        // Fallback: just check if directory is writable
        diskSpace = {
          free: null,
          total: null,
          used: null,
          writable: true
        };
      }

      res.json({
        success: true,
        data: {
          directory,
          diskSpace
        }
      } as ApiResponse);

    } catch (error) {
      console.error('Check disk space error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to check disk space'
      } as ApiResponse);
    }
  });

  // Get system logs (last N lines)
  router.get('/logs', async (req, res) => {
    try {
      const { lines = 100 } = req.query;
      const logs = await downloadManager.getRecentLogs(parseInt(lines as string));

      res.json({
        success: true,
        data: {
          logs,
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);

    } catch (error) {
      console.error('Get logs error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get logs'
      } as ApiResponse);
    }
  });

  return router;
}
