import axios, { AxiosInstance, AxiosResponse } from 'axios';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ServerStatus {
  server: {
    uptime: number;
    memory: any;
    version: string;
    nodeVersion: string;
    platform: string;
    arch: string;
  };
  downloads: {
    active: number;
    queued: number;
    total: number;
  };
  sldl: {
    available: boolean;
    version: string | null;
    path: string | null;
  };
}

export interface DownloadStats {
  totalDownloads: number;
  successfulDownloads: number;
  totalTracks: number;
  successfulTracks: number;
  totalSize: number;
  successRate: number;
  averageTracksPerDownload: number;
}

export class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor(baseURL = 'http://localhost:3001') {
    this.baseURL = baseURL;
    this.api = axios.create({
      baseURL: `${baseURL}/api`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log(`🌐 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('🌐 API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data);
        return response;
      },
      (error) => {
        console.error('❌ API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Health check
  async checkHealth(): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/health');
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Health check failed'
      };
    }
  }

  // Server status
  async getServerStatus(): Promise<ApiResponse<ServerStatus>> {
    try {
      const response = await this.api.get('/status');
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get server status'
      };
    }
  }

  // Download statistics
  async getDownloadStats(): Promise<ApiResponse<DownloadStats>> {
    try {
      const response = await this.api.get('/status/stats');
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get download stats'
      };
    }
  }

  // Configuration management
  async getConfig(): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/config');
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get configuration'
      };
    }
  }

  async saveConfig(config: any): Promise<ApiResponse> {
    try {
      const response = await this.api.post('/config', config);
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save configuration'
      };
    }
  }

  async testSpotifyCredentials(credentials: { spotifyClientId: string; spotifyClientSecret: string }): Promise<ApiResponse> {
    try {
      const response = await this.api.post('/config/test-spotify', credentials);
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to test Spotify credentials'
      };
    }
  }

  async testSoulseekCredentials(credentials: { soulseekUsername: string; soulseekPassword: string }): Promise<ApiResponse> {
    try {
      const response = await this.api.post('/config/test-soulseek', credentials);
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to test Soulseek credentials'
      };
    }
  }

  async validateDirectory(directory: string): Promise<ApiResponse> {
    try {
      const response = await this.api.post('/config/validate-directory', { directory });
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to validate directory'
      };
    }
  }

  // Download management
  async getActiveDownloads(): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/download/active');
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get active downloads'
      };
    }
  }

  async getDownloadHistory(limit = 50, offset = 0): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/download/history', {
        params: { limit, offset }
      });
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get download history'
      };
    }
  }

  async getDownloadStatus(requestId: string): Promise<ApiResponse> {
    try {
      const response = await this.api.get(`/download/status/${requestId}`);
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get download status'
      };
    }
  }

  // Disk space check
  async checkDiskSpace(directory: string): Promise<ApiResponse> {
    try {
      const response = await this.api.post('/status/disk-space', { directory });
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to check disk space'
      };
    }
  }

  // System logs
  async getSystemLogs(lines = 100): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/status/logs', {
        params: { lines }
      });
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get system logs'
      };
    }
  }

  // Test server connectivity
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.checkHealth();
      return response.success;
    } catch (error) {
      console.error('🌐 Server connection test failed:', error);
      return false;
    }
  }

  // Get base URL for display purposes
  getBaseURL(): string {
    return this.baseURL;
  }
}

// Create singleton instance
export const apiService = new ApiService();
