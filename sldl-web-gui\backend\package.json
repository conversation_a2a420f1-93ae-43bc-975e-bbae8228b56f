{"name": "sldl-web-gui-backend", "version": "1.0.0", "description": "Backend server for SLDL Web GUI", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src/**/*.ts", "test": "jest"}, "dependencies": {"express": "^4.21.2", "socket.io": "^4.8.1", "cors": "^2.8.5", "helmet": "^8.0.0", "dotenv": "^16.4.7", "uuid": "^11.0.4", "axios": "^1.11.0", "fs-extra": "^11.2.0", "path": "^0.12.7", "child_process": "^1.0.2"}, "devDependencies": {"@types/express": "^5.0.0", "@types/cors": "^2.8.17", "@types/uuid": "^10.0.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.10.5", "tsx": "^4.19.2", "typescript": "^5.8.3", "eslint": "^9.30.1", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "jest": "^29.7.0", "@types/jest": "^29.5.14"}, "keywords": ["sldl", "soulseek", "music", "downloader", "gui", "web"], "author": "SLDL GUI Team", "license": "MIT"}