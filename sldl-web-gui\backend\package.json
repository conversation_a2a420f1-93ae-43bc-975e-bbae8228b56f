{"name": "sldl-web-gui-backend", "version": "1.0.0", "description": "Backend server for SLDL Web GUI", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src/**/*.ts", "test": "jest"}, "dependencies": {"axios": "^1.11.0", "child_process": "^1.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "fs-extra": "^11.2.0", "helmet": "^8.0.0", "path": "^0.12.7", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.0.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/node": "^22.10.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "jest": "^29.7.0", "tsx": "^4.19.2", "typescript": "^5.8.3"}, "keywords": ["sldl", "soulseek", "music", "downloader", "gui", "web"], "author": "SLDL GUI Team", "license": "MIT"}