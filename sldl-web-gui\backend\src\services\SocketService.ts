import { Server as SocketIOServer, Socket } from 'socket.io';
import type { DownloadManager } from './DownloadManager.js';
import type { SocketEvents, DownloadRequest } from '../types/index.js';

export class SocketService {
  private io: SocketIOServer;
  private downloadManager: DownloadManager;
  private connectedClients = new Set<string>();

  constructor(io: SocketIOServer, downloadManager: DownloadManager) {
    this.io = io;
    this.downloadManager = downloadManager;
    this.setupSocketHandlers();
    this.setupDownloadManagerListeners();
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      console.log(`Client connected: ${socket.id}`);
      this.connectedClients.add(socket.id);

      // Send connection status
      socket.emit('connection:status', 'connected');

      // Send current active downloads to new client
      this.sendActiveDownloads(socket);

      // Handle download start
      socket.on('download:start', async (request: DownloadRequest) => {
        try {
          console.log(`Download start requested by ${socket.id}:`, request.id);
          await this.downloadManager.startDownload(request);
        } catch (error) {
          console.error('Download start error:', error);
          socket.emit('download:error', request.id, error instanceof Error ? error.message : 'Failed to start download');
        }
      });

      // Handle download pause
      socket.on('download:pause', async (requestId: string) => {
        try {
          console.log(`Download pause requested by ${socket.id}:`, requestId);
          await this.downloadManager.pauseDownload(requestId);
        } catch (error) {
          console.error('Download pause error:', error);
          socket.emit('download:error', requestId, error instanceof Error ? error.message : 'Failed to pause download');
        }
      });

      // Handle download resume
      socket.on('download:resume', async (requestId: string) => {
        try {
          console.log(`Download resume requested by ${socket.id}:`, requestId);
          await this.downloadManager.resumeDownload(requestId);
        } catch (error) {
          console.error('Download resume error:', error);
          socket.emit('download:error', requestId, error instanceof Error ? error.message : 'Failed to resume download');
        }
      });

      // Handle download cancel
      socket.on('download:cancel', async (requestId: string) => {
        try {
          console.log(`Download cancel requested by ${socket.id}:`, requestId);
          await this.downloadManager.cancelDownload(requestId);
        } catch (error) {
          console.error('Download cancel error:', error);
          socket.emit('download:error', requestId, error instanceof Error ? error.message : 'Failed to cancel download');
        }
      });

      // Handle download retry
      socket.on('download:retry', async (requestId: string, trackIds?: string[]) => {
        try {
          console.log(`Download retry requested by ${socket.id}:`, requestId, trackIds);
          await this.downloadManager.retryDownload(requestId, trackIds);
        } catch (error) {
          console.error('Download retry error:', error);
          socket.emit('download:error', requestId, error instanceof Error ? error.message : 'Failed to retry download');
        }
      });

      // Handle client disconnect
      socket.on('disconnect', (reason) => {
        console.log(`Client disconnected: ${socket.id}, reason: ${reason}`);
        this.connectedClients.delete(socket.id);
      });

      // Handle connection errors
      socket.on('error', (error) => {
        console.error(`Socket error for client ${socket.id}:`, error);
      });
    });
  }

  private setupDownloadManagerListeners(): void {
    // Listen for download progress updates
    this.downloadManager.on('download:progress', (progress) => {
      this.io.emit('download:progress', progress);
    });

    // Listen for individual job updates
    this.downloadManager.on('download:job-update', (job) => {
      this.io.emit('download:job-update', job);
    });

    // Listen for download completion
    this.downloadManager.on('download:completed', (requestId, summary) => {
      this.io.emit('download:completed', requestId, summary);
    });
  }

  private async sendActiveDownloads(socket: Socket): Promise<void> {
    try {
      const activeDownloads = await this.downloadManager.getActiveDownloads();
      
      // Send each active download's current progress
      for (const download of activeDownloads) {
        socket.emit('download:progress', download);
      }

      console.log(`Sent ${activeDownloads.length} active downloads to client ${socket.id}`);
    } catch (error) {
      console.error('Error sending active downloads:', error);
    }
  }

  // Broadcast a message to all connected clients
  public broadcast(event: string, data: any): void {
    this.io.emit(event, data);
  }

  // Send a message to a specific client
  public sendToClient(clientId: string, event: string, data: any): void {
    this.io.to(clientId).emit(event, data);
  }

  // Get connected client count
  public getConnectedClientCount(): number {
    return this.connectedClients.size;
  }

  // Get connected client IDs
  public getConnectedClients(): string[] {
    return Array.from(this.connectedClients);
  }

  // Cleanup method
  public cleanup(): void {
    console.log('Cleaning up SocketService');
    this.connectedClients.clear();
    this.downloadManager.removeAllListeners();
  }
}
