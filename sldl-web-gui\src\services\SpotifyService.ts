interface SpotifyCredentials {
  clientId: string;
  clientSecret: string;
}

interface SpotifyPlaylistResponse {
  id: string;
  name: string;
  description: string;
  owner: {
    display_name: string;
  };
  public: boolean;
  tracks: {
    total: number;
    items: Array<{
      track: {
        id: string;
        name: string;
        artists: Array<{ name: string }>;
        album: { name: string };
        duration_ms: number;
        preview_url: string | null;
      };
    }>;
  };
  images: Array<{ url: string; height: number; width: number }>;
}

interface PlaylistInfo {
  id: string;
  name: string;
  description: string;
  owner: string;
  trackCount: number;
  duration: string;
  isPublic: boolean;
  imageUrl?: string;
  tracks: Track[];
}

interface Track {
  id: string;
  name: string;
  artists: string[];
  album: string;
  duration: string;
  previewUrl?: string;
}

class SpotifyService {
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  /**
   * Get access token using Client Credentials flow
   */
  private async getAccessToken(credentials: SpotifyCredentials): Promise<string> {
    // Check if we have a valid token
    if (this.accessToken && Date.now() < this.tokenExpiry) {
      return this.accessToken;
    }

    console.log('Getting new Spotify access token...');

    const response = await fetch('https://accounts.spotify.com/api/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${btoa(`${credentials.clientId}:${credentials.clientSecret}`)}`,
      },
      body: 'grant_type=client_credentials',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Spotify authentication failed: ${error.error_description || error.error}`);
    }

    const data = await response.json();
    this.accessToken = data.access_token;
    this.tokenExpiry = Date.now() + (data.expires_in * 1000) - 60000; // Subtract 1 minute for safety

    console.log('Successfully obtained Spotify access token');
    return this.accessToken;
  }

  /**
   * Extract playlist ID from Spotify URL or URI
   */
  extractPlaylistId(url: string): string | null {
    // Extract from https://open.spotify.com/playlist/ID?si=...
    const urlMatch = url.match(/https:\/\/open\.spotify\.com\/playlist\/([a-zA-Z0-9]+)/);
    if (urlMatch) return urlMatch[1];
    
    // Extract from spotify:playlist:ID
    const uriMatch = url.match(/spotify:playlist:([a-zA-Z0-9]+)/);
    if (uriMatch) return uriMatch[1];
    
    return null;
  }

  /**
   * Format duration from milliseconds to MM:SS
   */
  private formatDuration(durationMs: number): string {
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * Calculate total playlist duration
   */
  private calculateTotalDuration(tracks: Array<{ track: { duration_ms: number } }>): string {
    const totalMs = tracks.reduce((sum, item) => sum + (item.track?.duration_ms || 0), 0);
    const totalMinutes = Math.floor(totalMs / 60000);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  /**
   * Get playlist information from Spotify API with full pagination
   */
  async getPlaylistInfo(playlistUrl: string, credentials: SpotifyCredentials): Promise<PlaylistInfo> {
    const playlistId = this.extractPlaylistId(playlistUrl);
    if (!playlistId) {
      throw new Error('Invalid Spotify playlist URL');
    }

    console.log(`Fetching playlist info for ID: ${playlistId}`);

    const accessToken = await this.getAccessToken(credentials);

    // Get playlist basic info first
    const playlistResponse = await fetch(`https://api.spotify.com/v1/playlists/${playlistId}?fields=id,name,description,owner.display_name,public,images,tracks.total`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    if (!playlistResponse.ok) {
      if (playlistResponse.status === 404) {
        throw new Error('Playlist not found. Make sure the playlist is public or you have access to it.');
      }
      const error = await playlistResponse.json();
      throw new Error(`Failed to fetch playlist: ${error.error?.message || 'Unknown error'}`);
    }

    const playlist = await playlistResponse.json();
    const totalTracks = playlist.tracks.total;

    console.log(`Successfully fetched playlist: "${playlist.name}" with ${totalTracks} tracks`);

    // Now fetch all tracks with pagination (like the C# version does)
    const allTracks: Track[] = [];
    let offset = 0;
    const limit = 100; // Maximum allowed by Spotify API

    while (offset < totalTracks) {
      console.log(`Fetching tracks ${offset + 1}-${Math.min(offset + limit, totalTracks)} of ${totalTracks}...`);

      const tracksResponse = await fetch(`https://api.spotify.com/v1/playlists/${playlistId}/tracks?fields=items(track(id,name,artists.name,album.name,duration_ms,preview_url))&limit=${limit}&offset=${offset}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      if (!tracksResponse.ok) {
        const error = await tracksResponse.json();
        throw new Error(`Failed to fetch tracks: ${error.error?.message || 'Unknown error'}`);
      }

      const tracksData = await tracksResponse.json();

      // Convert tracks to our format
      const tracks: Track[] = tracksData.items
        .filter((item: any) => item.track && item.track.id) // Filter out null tracks
        .map((item: any) => ({
          id: item.track.id,
          name: item.track.name,
          artists: item.track.artists.map((artist: any) => artist.name),
          album: item.track.album.name,
          duration: this.formatDuration(item.track.duration_ms),
          previewUrl: item.track.preview_url || undefined,
        }));

      allTracks.push(...tracks);
      offset += limit;

      // Add a small delay to be respectful to the API
      if (offset < totalTracks) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    console.log(`✅ Successfully loaded all ${allTracks.length} tracks from "${playlist.name}"`);

    // Calculate total duration from all tracks
    const totalDurationMs = allTracks.reduce((sum, track) => {
      const [minutes, seconds] = track.duration.split(':').map(Number);
      return sum + (minutes * 60 + seconds) * 1000;
    }, 0);

    const duration = this.formatDuration(totalDurationMs);

    return {
      id: playlist.id,
      name: playlist.name,
      description: playlist.description || '',
      owner: playlist.owner.display_name,
      trackCount: allTracks.length, // Use actual count of loaded tracks
      duration,
      isPublic: playlist.public,
      imageUrl: playlist.images?.[0]?.url,
      tracks: allTracks,
    };
  }

  /**
   * Validate Spotify credentials by attempting to get an access token
   */
  async validateCredentials(credentials: SpotifyCredentials): Promise<boolean> {
    try {
      await this.getAccessToken(credentials);
      return true;
    } catch (error) {
      console.error('Spotify credential validation failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const spotifyService = new SpotifyService();

// Export types
export type { SpotifyCredentials, PlaylistInfo, Track };
