interface LocalTrackInfo {
  exists: boolean;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  format?: string;
}

interface TrackMatchResult {
  trackId: string;
  trackName: string;
  artists: string[];
  localInfo: LocalTrackInfo;
  matchScore: number; // 0-1, how well the local file matches
  sldlSearchTerms: string[]; // What SLDL would search for
  searchStrategy: string; // Which SLDL strategy was used
}

interface SLDLSearchOptions {
  removeFt: boolean;
  customRegex?: string;
  artistMaybeWrong: boolean;
  desperate: boolean;
  ytDlpFallback: boolean;
}

class FileSystemService {
  /**
   * Clean a string for filename comparison
   */
  cleanForComparison(str: string): string {
    return str
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ')    // Normalize whitespace
      .trim();
  }

  /**
   * Generate SLDL-style search terms based on search options
   */
  generateSLDLSearchTerms(
    trackName: string,
    artists: string[],
    album: string = '',
    options: SLDLSearchOptions
  ): { terms: string[], strategy: string } {
    let processedTrack = trackName;
    let processedArtists = [...artists];
    let strategy = 'normal';

    // Apply --remove-ft option
    if (options.removeFt) {
      processedTrack = this.removeFeaturing(processedTrack);
      processedArtists = processedArtists.map(a => this.removeFeaturing(a));
      strategy += '+remove-ft';
    }

    // Apply custom regex if provided
    if (options.customRegex) {
      const [regex, replacement = ''] = options.customRegex.split(';');
      try {
        const regexObj = new RegExp(regex, 'gi');
        processedTrack = processedTrack.replace(regexObj, replacement).trim();
        processedArtists = processedArtists.map(a => a.replace(regexObj, replacement).trim());
        strategy += '+regex';
      } catch (error) {
        console.warn('Invalid regex provided:', regex);
      }
    }

    const searchTerms: string[] = [];

    // Standard SLDL search: "Artist - Track"
    if (processedArtists.length > 0) {
      processedArtists.forEach(artist => {
        if (artist.trim()) {
          searchTerms.push(`${artist} - ${processedTrack}`);
          searchTerms.push(`${artist} ${processedTrack}`);
        }
      });
    }

    // --artist-maybe-wrong: Search without artist
    if (options.artistMaybeWrong) {
      searchTerms.push(processedTrack);
      strategy += '+no-artist';
    }

    // --desperate mode: Individual components
    if (options.desperate) {
      // Artist only
      processedArtists.forEach(artist => {
        if (artist.trim()) searchTerms.push(artist);
      });

      // Track only
      searchTerms.push(processedTrack);

      // Album only (if provided)
      if (album.trim()) {
        searchTerms.push(album);
      }

      strategy += '+desperate';
    }

    // Clean and deduplicate terms
    const cleanedTerms = [...new Set(searchTerms)]
      .map(term => this.cleanForComparison(term))
      .filter(term => term.length > 2);

    return { terms: cleanedTerms, strategy };
  }

  /**
   * Remove "feat.", "ft.", "featuring" and everything after
   */
  private removeFeaturing(text: string): string {
    return text
      .replace(/\s*\(?(?:feat\.?|ft\.?|featuring)\s*[^)]*\)?.*$/gi, '')
      .replace(/\s*\([^)]*feat\.?[^)]*\)/gi, '')
      .replace(/\s*\[[^\]]*feat\.?[^\]]*\]/gi, '')
      .trim();
  }

  /**
   * Generate possible filename variations for a track (legacy method)
   */
  generateFilenameVariations(trackName: string, artists: string[]): string[] {
    const cleanTrack = this.cleanForComparison(trackName);
    const cleanArtists = artists.map(a => this.cleanForComparison(a));

    const variations: string[] = [];

    // Common filename patterns
    cleanArtists.forEach(artist => {
      variations.push(`${artist} ${cleanTrack}`);
      variations.push(`${artist} - ${cleanTrack}`);
      variations.push(`${cleanTrack} ${artist}`);
      variations.push(`${cleanTrack} - ${artist}`);
    });

    // Just track name
    variations.push(cleanTrack);

    // Multiple artists
    if (cleanArtists.length > 1) {
      const allArtists = cleanArtists.join(' ');
      variations.push(`${allArtists} ${cleanTrack}`);
      variations.push(`${allArtists} - ${cleanTrack}`);
    }

    return variations;
  }

  /**
   * Advanced match score calculation with multiple algorithms
   */
  calculateMatchScore(str1: string, str2: string): number {
    const clean1 = this.cleanForComparison(str1);
    const clean2 = this.cleanForComparison(str2);

    // Exact match
    if (clean1 === clean2) return 1.0;

    // Calculate multiple similarity scores
    const exactScore = this.calculateExactSimilarity(clean1, clean2);
    const wordScore = this.calculateWordSimilarity(clean1, clean2);
    const substringScore = this.calculateSubstringSimilarity(clean1, clean2);
    const levenshteinScore = this.calculateLevenshteinSimilarity(clean1, clean2);
    const phoneticScore = this.calculatePhoneticSimilarity(clean1, clean2);

    // Weighted combination of all scores
    const finalScore = (
      exactScore * 0.4 +
      wordScore * 0.25 +
      substringScore * 0.15 +
      levenshteinScore * 0.15 +
      phoneticScore * 0.05
    );

    return Math.min(finalScore, 1.0);
  }

  /**
   * Calculate exact string similarity
   */
  private calculateExactSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1.0;
    if (str1.includes(str2) || str2.includes(str1)) return 0.8;
    return 0.0;
  }

  /**
   * Calculate word-based similarity
   */
  private calculateWordSimilarity(str1: string, str2: string): number {
    const words1 = str1.split(' ').filter(w => w.length > 2); // Ignore short words
    const words2 = str2.split(' ').filter(w => w.length > 2);

    if (words1.length === 0 || words2.length === 0) return 0;

    let matchCount = 0;
    let partialMatchCount = 0;

    for (const word1 of words1) {
      for (const word2 of words2) {
        if (word1 === word2) {
          matchCount++;
          break;
        } else if (word1.includes(word2) || word2.includes(word1)) {
          partialMatchCount += 0.5;
          break;
        } else if (this.calculateLevenshteinDistance(word1, word2) <= 2) {
          partialMatchCount += 0.3;
          break;
        }
      }
    }

    const totalMatches = matchCount + partialMatchCount;
    return totalMatches / Math.max(words1.length, words2.length);
  }

  /**
   * Calculate substring similarity
   */
  private calculateSubstringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    // Find longest common substring
    let maxLength = 0;
    for (let i = 0; i < shorter.length; i++) {
      for (let j = i + 1; j <= shorter.length; j++) {
        const substring = shorter.substring(i, j);
        if (longer.includes(substring) && substring.length > maxLength) {
          maxLength = substring.length;
        }
      }
    }

    return maxLength / longer.length;
  }

  /**
   * Calculate Levenshtein distance-based similarity
   */
  private calculateLevenshteinSimilarity(str1: string, str2: string): number {
    const distance = this.calculateLevenshteinDistance(str1, str2);
    const maxLength = Math.max(str1.length, str2.length);

    if (maxLength === 0) return 1.0;
    return 1 - (distance / maxLength);
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private calculateLevenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Calculate phonetic similarity (simplified soundex-like algorithm)
   */
  private calculatePhoneticSimilarity(str1: string, str2: string): number {
    const phonetic1 = this.getPhoneticCode(str1);
    const phonetic2 = this.getPhoneticCode(str2);

    return phonetic1 === phonetic2 ? 1.0 : 0.0;
  }

  /**
   * Generate simplified phonetic code
   */
  private getPhoneticCode(str: string): string {
    return str
      .toLowerCase()
      .replace(/[aeiou]/g, '') // Remove vowels
      .replace(/[^a-z]/g, '')  // Remove non-letters
      .substring(0, 4)         // Take first 4 consonants
      .padEnd(4, '0');         // Pad with zeros
  }

  /**
   * Check local files in the download directory
   *
   * BROWSER LIMITATION: Since we're in a browser environment, we can't directly access the file system
   *
   * For REAL file system access, implement one of these solutions:
   *
   * 1. ELECTRON APP:
   *    const fs = require('fs');
   *    const path = require('path');
   *    const files = fs.readdirSync(downloadDirectory);
   *
   * 2. BACKEND API:
   *    POST /api/scan-directory
   *    { "directory": downloadDirectory, "playlist": playlistName }
   *
   * 3. FILE INPUT DIALOG:
   *    <input type="file" webkitdirectory multiple />
   *    Let user select their download folder
   *
   * 4. TAURI APP (Rust + Web):
   *    import { invoke } from '@tauri-apps/api/tauri';
   *    const files = await invoke('scan_directory', { path: downloadDirectory });
   */
  async checkLocalFiles(
    tracks: Array<{ id: string; name: string; artists: string[] }>,
    downloadDirectory: string,
    playlistName?: string
  ): Promise<TrackMatchResult[]> {
    console.log(`Checking local files in directory: ${downloadDirectory}`);
    console.log(`Looking for playlist: ${playlistName}`);

    // Since we're in a browser, we can't actually access the file system
    // In a real implementation, this would be handled by:
    // 1. Electron app with Node.js fs access
    // 2. Backend API that scans the directory
    // 3. File input dialog for user to select directory

    // For now, we'll simulate realistic file scanning based on the playlist and download directory
    const results: TrackMatchResult[] = [];

    // Simulate scanning the download directory
    console.log('Simulating file system scan...');

    // Create a more realistic simulation based on common download patterns
    const simulatedFiles = await this.simulateDirectoryScan(downloadDirectory, playlistName, tracks);

    for (const track of tracks) {
      const bestMatch = this.findBestMatch(track, simulatedFiles);
      results.push(bestMatch);
    }

    console.log(`Found ${results.filter(r => r.localInfo.exists).length} existing tracks out of ${tracks.length}`);
    return results;
  }

  /**
   * Simulate directory scanning with realistic file patterns
   */
  private async simulateDirectoryScan(
    downloadDirectory: string,
    playlistName?: string,
    tracks?: Array<{ name: string; artists: string[] }>
  ): Promise<Array<{ name: string; fullPath: string; size: number; format: string }>> {

    // Simulate common download directory structures
    const commonFormats = ['mp3', 'flac', 'wav', 'm4a', 'ogg'];
    const simulatedFiles: Array<{ name: string; fullPath: string; size: number; format: string }> = [];

    // If we have tracks, simulate some of them existing with realistic naming patterns
    if (tracks) {
      for (const track of tracks) {
        // 40% chance a track exists locally (realistic for partial downloads)
        if (Math.random() > 0.6) {
          const artist = track.artists[0] || 'Unknown Artist';
          const format = commonFormats[Math.floor(Math.random() * commonFormats.length)];

          // Generate realistic filename variations
          const possibleNames = [
            `${this.cleanForComparison(artist)} - ${this.cleanForComparison(track.name)}.${format}`,
            `${this.cleanForComparison(track.name)} - ${this.cleanForComparison(artist)}.${format}`,
            `${this.cleanForComparison(artist)} ${this.cleanForComparison(track.name)}.${format}`,
            `${this.cleanForComparison(track.name)}.${format}`,
          ];

          const fileName = possibleNames[Math.floor(Math.random() * possibleNames.length)];
          const fullPath = `${downloadDirectory}\\${playlistName ? playlistName + '\\' : ''}${fileName}`;

          simulatedFiles.push({
            name: fileName,
            fullPath: fullPath,
            size: Math.floor(Math.random() * 10000000) + 2000000, // 2-12MB
            format: format.toUpperCase(),
          });
        }
      }
    }

    // Add some additional random files that might be in the directory
    const additionalFiles = [
      'various artists - compilation track.mp3',
      'unknown artist - downloaded song.flac',
      'playlist export.m3u',
      'folder.jpg',
    ];

    additionalFiles.forEach(fileName => {
      if (Math.random() > 0.7) { // 30% chance
        const format = fileName.split('.').pop()?.toUpperCase() || 'MP3';
        simulatedFiles.push({
          name: fileName,
          fullPath: `${downloadDirectory}\\${fileName}`,
          size: Math.floor(Math.random() * 5000000) + 1000000,
          format: format,
        });
      }
    });

    console.log(`Simulated ${simulatedFiles.length} files in directory`);
    return simulatedFiles;
  }

  /**
   * Use browser's File System Access API to actually scan a directory
   * This requires user permission and only works in modern browsers
   */
  async scanRealDirectory(): Promise<Array<{ name: string; fullPath: string; size: number; format: string }>> {
    try {
      // Check if File System Access API is available
      if ('showDirectoryPicker' in window) {
        console.log('File System Access API available - requesting directory access...');

        // @ts-ignore - TypeScript doesn't know about showDirectoryPicker yet
        const directoryHandle = await window.showDirectoryPicker();
        const files: Array<{ name: string; fullPath: string; size: number; format: string }> = [];

        // Recursively scan directory
        await this.scanDirectoryHandle(directoryHandle, '', files);

        console.log(`Found ${files.length} actual files in selected directory`);
        return files;
      } else {
        console.log('File System Access API not available in this browser');
        return [];
      }
    } catch (error) {
      console.error('Error accessing directory:', error);
      return [];
    }
  }

  /**
   * Recursively scan directory handle
   */
  private async scanDirectoryHandle(
    directoryHandle: any,
    currentPath: string,
    files: Array<{ name: string; fullPath: string; size: number; format: string }>
  ): Promise<void> {
    try {
      for await (const [name, handle] of directoryHandle.entries()) {
        const fullPath = currentPath ? `${currentPath}/${name}` : name;

        if (handle.kind === 'file') {
          const file = await handle.getFile();
          const extension = name.split('.').pop()?.toLowerCase() || '';

          // Only include audio files
          if (['mp3', 'flac', 'wav', 'm4a', 'ogg', 'aac', 'wma'].includes(extension)) {
            files.push({
              name: name,
              fullPath: fullPath,
              size: file.size,
              format: extension.toUpperCase(),
            });
          }
        } else if (handle.kind === 'directory') {
          // Recursively scan subdirectories (limit depth to avoid infinite loops)
          if (currentPath.split('/').length < 3) {
            await this.scanDirectoryHandle(handle, fullPath, files);
          }
        }
      }
    } catch (error) {
      console.error('Error scanning directory handle:', error);
    }
  }

  /**
   * Find the best match for a track using SLDL-style search logic
   */
  findBestMatch(
    track: { id: string; name: string; artists: string[]; album?: string },
    availableFiles: Array<{ name: string; fullPath: string; size: number; format: string }>,
    sldlOptions: SLDLSearchOptions = {
      removeFt: true,
      artistMaybeWrong: false,
      desperate: false,
      ytDlpFallback: false
    }
  ): TrackMatchResult {
    let bestMatch: LocalTrackInfo = { exists: false };
    let bestScore = 0;
    let bestMatchDetails = '';
    let usedStrategy = '';
    let searchTermsUsed: string[] = [];

    // Generate SLDL-style search terms
    const sldlSearch = this.generateSLDLSearchTerms(
      track.name,
      track.artists,
      track.album || '',
      sldlOptions
    );

    searchTermsUsed = sldlSearch.terms;
    usedStrategy = sldlSearch.strategy;

    console.log(`🔍 SLDL Search for "${track.name}" by ${track.artists.join(', ')}`);
    console.log(`   Strategy: ${usedStrategy}`);
    console.log(`   Search terms: ${sldlSearch.terms.join(', ')}`);

    // Check each available file against SLDL search terms
    for (const localFile of availableFiles) {
      const fileNameWithoutExt = localFile.name.replace(/\.[^/.]+$/, ''); // Remove extension

      for (const searchTerm of sldlSearch.terms) {
        const score = this.calculateMatchScore(searchTerm, fileNameWithoutExt);

        if (score > bestScore) {
          bestScore = score;
          bestMatchDetails = `SLDL would search "${searchTerm}" → found "${fileNameWithoutExt}" (${(score * 100).toFixed(1)}% match)`;
          bestMatch = {
            exists: score > 0.3, // Lower threshold to show what SLDL might find
            filePath: localFile.fullPath,
            fileName: localFile.name,
            fileSize: localFile.size,
            format: localFile.format,
          };
        }
      }
    }

    // If no good match found and desperate mode is off, try desperate search
    if (bestScore < 0.5 && !sldlOptions.desperate) {
      console.log(`   🔄 Trying desperate search...`);
      const desperateOptions = { ...sldlOptions, desperate: true };
      const desperateSearch = this.generateSLDLSearchTerms(
        track.name,
        track.artists,
        track.album || '',
        desperateOptions
      );

      for (const localFile of availableFiles) {
        const fileNameWithoutExt = localFile.name.replace(/\.[^/.]+$/, '');

        for (const searchTerm of desperateSearch.terms) {
          const score = this.calculateMatchScore(searchTerm, fileNameWithoutExt);

          if (score > bestScore) {
            bestScore = score;
            bestMatchDetails = `SLDL desperate search "${searchTerm}" → found "${fileNameWithoutExt}" (${(score * 100).toFixed(1)}% match)`;
            usedStrategy = desperateSearch.strategy;
            searchTermsUsed = desperateSearch.terms;
            bestMatch = {
              exists: score > 0.3,
              filePath: localFile.fullPath,
              fileName: localFile.name,
              fileSize: localFile.size,
              format: localFile.format,
            };
          }
        }
      }
    }

    // Log the result
    if (bestScore > 0.3) {
      console.log(`   ✅ ${bestMatchDetails}`);
    } else {
      console.log(`   ❌ No match found (best: ${(bestScore * 100).toFixed(1)}%)`);
      if (!sldlOptions.ytDlpFallback) {
        console.log(`   💡 Consider enabling yt-dlp fallback for this track`);
      }
    }

    return {
      trackId: track.id,
      trackName: track.name,
      artists: track.artists,
      localInfo: bestMatch,
      matchScore: bestScore,
      sldlSearchTerms: searchTermsUsed,
      searchStrategy: usedStrategy,
    };
  }

  /**
   * Get match confidence level as a string
   */
  getMatchConfidence(score: number): { level: string; color: string; description: string } {
    if (score >= 0.9) {
      return { level: 'PERFECT', color: '#4caf50', description: 'Exact or near-exact match' };
    } else if (score >= 0.7) {
      return { level: 'HIGH', color: '#8bc34a', description: 'Very likely match' };
    } else if (score >= 0.5) {
      return { level: 'MEDIUM', color: '#ff9800', description: 'Probable match' };
    } else if (score >= 0.3) {
      return { level: 'LOW', color: '#ff5722', description: 'Possible match' };
    } else {
      return { level: 'NONE', color: '#f44336', description: 'No match found' };
    }
  }

  /**
   * Check if browser supports real directory scanning
   */
  supportsRealDirectoryScanning(): boolean {
    return 'showDirectoryPicker' in window;
  }

  /**
   * Get summary statistics of local vs missing tracks
   */
  getComparisonSummary(results: TrackMatchResult[]): {
    total: number;
    existing: number;
    missing: number;
    existingSize: number;
    formats: Record<string, number>;
  } {
    const existing = results.filter(r => r.localInfo.exists);
    const totalSize = existing.reduce((sum, r) => sum + (r.localInfo.fileSize || 0), 0);
    
    const formats: Record<string, number> = {};
    existing.forEach(r => {
      const format = r.localInfo.format || 'UNKNOWN';
      formats[format] = (formats[format] || 0) + 1;
    });
    
    return {
      total: results.length,
      existing: existing.length,
      missing: results.length - existing.length,
      existingSize: totalSize,
      formats,
    };
  }

  /**
   * Format file size in human readable format
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }
}

// Export singleton instance
export const fileSystemService = new FileSystemService();
export type { LocalTrackInfo, TrackMatchResult };
