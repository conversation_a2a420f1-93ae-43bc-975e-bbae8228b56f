import { spotifyService } from './SpotifyService';
import type { PlaylistInfo, Track } from './SpotifyService';

interface SLDLTrackInfo {
  title: string;
  artist: string;
  album: string;
  length: number; // in seconds
  uri?: string;
  itemNumber: number;
  lineNumber?: number;
}

interface SLDLTrackListEntry {
  source: {
    type: 'Normal' | 'Album' | 'Aggregate';
    url: string;
    itemName?: string;
  };
  tracks: SLDLTrackInfo[];
  extractorCond?: string;
  extractorPrefCond?: string;
  enablesIndexByDefault: boolean;
  sourceCanBeSkipped: boolean;
}

interface SLDLTrackLists {
  lists: SLDLTrackListEntry[];
}

/**
 * Service that mimics SLDL's extractor registry and track extraction logic
 * This helps us understand exactly how SLDL would process different inputs
 */
class SLDLExtractorService {
  
  /**
   * Detect input type just like SLDL's ExtractorRegistry
   */
  detectInputType(input: string): { type: string; extractor: string } {
    const trimmedInput = input.trim();
    
    // Spotify playlist/album/track
    if (this.isSpotifyUrl(trimmedInput)) {
      if (trimmedInput.includes('/playlist/')) {
        return { type: 'spotify_playlist', extractor: 'Spotify' };
      } else if (trimmedInput.includes('/album/')) {
        return { type: 'spotify_album', extractor: 'Spotify' };
      } else if (trimmedInput.includes('/track/')) {
        return { type: 'spotify_track', extractor: 'Spotify' };
      }
      return { type: 'spotify_unknown', extractor: 'Spotify' };
    }
    
    // YouTube playlist/video
    if (this.isYouTubeUrl(trimmedInput)) {
      if (trimmedInput.includes('playlist?list=') || trimmedInput.includes('&list=')) {
        return { type: 'youtube_playlist', extractor: 'YouTube' };
      }
      return { type: 'youtube_video', extractor: 'YouTube' };
    }
    
    // Soulseek direct link
    if (trimmedInput.startsWith('slsk://')) {
      return { type: 'soulseek_direct', extractor: 'Soulseek' };
    }
    
    // File-based inputs
    if (trimmedInput.endsWith('.csv')) {
      return { type: 'csv_file', extractor: 'CSV' };
    }
    
    if (trimmedInput.endsWith('.txt') || trimmedInput.endsWith('.m3u') || trimmedInput.endsWith('.m3u8')) {
      return { type: 'list_file', extractor: 'List' };
    }
    
    // Default to string/search
    return { type: 'string_search', extractor: 'String' };
  }
  
  /**
   * Extract tracks from input using SLDL-like logic
   */
  async extractTracks(
    input: string, 
    maxTracks: number = Number.MAX_SAFE_INTEGER,
    offset: number = 0,
    reverse: boolean = false,
    config: any = {}
  ): Promise<SLDLTrackLists> {
    
    const inputInfo = this.detectInputType(input);
    console.log(`🎵 SLDL would use ${inputInfo.extractor} extractor for: ${inputInfo.type}`);
    
    switch (inputInfo.extractor) {
      case 'Spotify':
        return await this.extractSpotifyTracks(input, maxTracks, offset, reverse, config);
      
      case 'YouTube':
        return await this.extractYouTubeTracks(input, maxTracks, offset, reverse, config);
      
      case 'Soulseek':
        return await this.extractSoulseekTracks(input, maxTracks, offset, reverse, config);
      
      case 'List':
        return await this.extractListTracks(input, maxTracks, offset, reverse, config);
      
      case 'CSV':
        return await this.extractCsvTracks(input, maxTracks, offset, reverse, config);
      
      default:
        return await this.extractStringTracks(input, maxTracks, offset, reverse, config);
    }
  }
  
  /**
   * Extract Spotify playlist tracks (mimics Spotify.cs)
   */
  private async extractSpotifyTracks(
    input: string, 
    maxTracks: number, 
    offset: number, 
    reverse: boolean, 
    config: any
  ): Promise<SLDLTrackLists> {
    
    console.log('📀 SLDL: Loading Spotify playlist...');
    
    try {
      // Use our existing Spotify service but format like SLDL
      const playlistInfo = await spotifyService.getPlaylistInfo(input, {
        clientId: config.spotifyClientId || '',
        clientSecret: config.spotifyClientSecret || '',
      });
      
      if (!playlistInfo) {
        throw new Error('Spotify playlist not found (may be private or invalid URL)');
      }
      
      // Convert to SLDL format
      const sldlTracks: SLDLTrackInfo[] = playlistInfo.tracks.map((track, index) => ({
        title: track.name,
        artist: track.artists.join(', '),
        album: track.album,
        length: this.parseDuration(track.duration),
        uri: `spotify:track:${track.id}`,
        itemNumber: offset + index + 1,
      }));
      
      // Apply offset, limit, and reverse like SLDL does
      let processedTracks = sldlTracks;
      if (reverse) {
        processedTracks = [...sldlTracks].reverse();
      }
      
      processedTracks = processedTracks.slice(offset, offset + maxTracks);
      
      const trackListEntry: SLDLTrackListEntry = {
        source: {
          type: 'Normal',
          url: input,
          itemName: playlistInfo.name,
        },
        tracks: processedTracks,
        enablesIndexByDefault: true,
        sourceCanBeSkipped: false,
      };
      
      console.log(`✅ SLDL: Loaded ${processedTracks.length} tracks from "${playlistInfo.name}"`);
      
      return {
        lists: [trackListEntry]
      };
      
    } catch (error) {
      console.error('❌ SLDL: Spotify extraction failed:', error);
      throw error;
    }
  }
  
  /**
   * Extract YouTube playlist tracks (placeholder - mimics YouTube.cs)
   */
  private async extractYouTubeTracks(
    input: string, 
    maxTracks: number, 
    offset: number, 
    reverse: boolean, 
    config: any
  ): Promise<SLDLTrackLists> {
    
    console.log('📺 SLDL: Loading YouTube playlist...');
    
    // This would integrate with YouTube API or yt-dlp
    // For now, return empty structure
    return {
      lists: [{
        source: {
          type: 'Normal',
          url: input,
          itemName: 'YouTube Playlist',
        },
        tracks: [],
        enablesIndexByDefault: true,
        sourceCanBeSkipped: false,
      }]
    };
  }
  
  /**
   * Extract Soulseek direct tracks (mimics Soulseek.cs)
   */
  private async extractSoulseekTracks(
    input: string, 
    maxTracks: number, 
    offset: number, 
    reverse: boolean, 
    config: any
  ): Promise<SLDLTrackLists> {
    
    console.log('🎧 SLDL: Processing Soulseek direct link...');
    
    // Parse slsk://username/path format
    const uri = decodeURIComponent(input);
    const parts = uri.slice('slsk://'.length).split('/', 2);
    const username = parts[0];
    const path = parts[1]?.replace(/\//g, '\\');
    
    const trackListEntry: SLDLTrackListEntry = {
      source: {
        type: 'Normal',
        url: input,
      },
      tracks: [{
        title: path ? path.split('\\').pop()?.replace(/\.[^.]+$/, '') || 'Unknown' : 'Unknown',
        artist: username,
        album: '',
        length: 0,
        uri: input,
        itemNumber: 1,
      }],
      enablesIndexByDefault: false,
      sourceCanBeSkipped: false,
    };
    
    return {
      lists: [trackListEntry]
    };
  }
  
  /**
   * Extract from list file (mimics List.cs)
   */
  private async extractListTracks(
    input: string, 
    maxTracks: number, 
    offset: number, 
    reverse: boolean, 
    config: any
  ): Promise<SLDLTrackLists> {
    
    console.log('📄 SLDL: Loading list file...');
    
    // This would read from actual file system
    // For browser, we can't access files directly
    throw new Error('List file extraction not supported in browser environment');
  }
  
  /**
   * Extract from CSV file (mimics Csv.cs)
   */
  private async extractCsvTracks(
    input: string, 
    maxTracks: number, 
    offset: number, 
    reverse: boolean, 
    config: any
  ): Promise<SLDLTrackLists> {
    
    console.log('📊 SLDL: Loading CSV file...');
    
    // This would read from actual file system
    // For browser, we can't access files directly
    throw new Error('CSV file extraction not supported in browser environment');
  }
  
  /**
   * Extract from string search (mimics String.cs)
   */
  private async extractStringTracks(
    input: string, 
    maxTracks: number, 
    offset: number, 
    reverse: boolean, 
    config: any
  ): Promise<SLDLTrackLists> {
    
    console.log('🔍 SLDL: Processing string search...');
    
    // Parse "Artist - Title" format
    const parts = input.split(' - ');
    const artist = parts.length > 1 ? parts[0].trim() : '';
    const title = parts.length > 1 ? parts.slice(1).join(' - ').trim() : input.trim();
    
    const trackListEntry: SLDLTrackListEntry = {
      source: {
        type: 'Normal',
        url: input,
      },
      tracks: [{
        title: title,
        artist: artist,
        album: '',
        length: 0,
        itemNumber: 1,
      }],
      enablesIndexByDefault: true,
      sourceCanBeSkipped: true,
    };
    
    return {
      lists: [trackListEntry]
    };
  }
  
  /**
   * Helper methods
   */
  private isSpotifyUrl(url: string): boolean {
    return url.includes('spotify.com') || url.startsWith('spotify:');
  }
  
  private isYouTubeUrl(url: string): boolean {
    return url.includes('youtube.com') || url.includes('youtu.be');
  }
  
  private parseDuration(duration: string): number {
    // Convert "3:20" to seconds
    const parts = duration.split(':').map(p => parseInt(p));
    if (parts.length === 2) {
      return parts[0] * 60 + parts[1];
    }
    return 0;
  }
}

// Export singleton instance
export const sldlExtractorService = new SLDLExtractorService();
export type { SLDLTrackInfo, SLDLTrackListEntry, SLDLTrackLists };
