# SLDL Web GUI Backend Configuration

# Server Configuration
PORT=3001
HOST=localhost
NODE_ENV=development

# Frontend URL for CORS
FRONTEND_URL=http://localhost:5173

# SLDL CLI Configuration
SLDL_CLI_PATH=slsk-batchdl
# Alternative: Absolute path to SLDL CLI executable
# SLDL_CLI_PATH=C:\path\to\slsk-batchdl.exe

# Download Configuration
MAX_CONCURRENT_DOWNLOADS=5
MAX_RETRIES=3
DOWNLOAD_TIMEOUT_MS=300000

# Temporary Directory
TEMP_DIR=./temp

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/sldl-gui.log

# Security
SESSION_SECRET=your-secret-key-here
ENABLE_RATE_LIMITING=true

# Optional: Database Configuration (for future use)
# DATABASE_URL=sqlite:./data/sldl-gui.db
