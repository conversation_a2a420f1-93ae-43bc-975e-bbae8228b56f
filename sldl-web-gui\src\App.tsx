import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { spotifyService } from './services/SpotifyService';
import { fileSystemService } from './services/FileSystemService';
import { sldlExtractorService } from './services/SLDLExtractorService';
import { webSocketService } from './services/WebSocketService';
import { apiService } from './services/ApiService';
import type { PlaylistInfo, Track } from './services/SpotifyService';
import type { TrackMatchResult } from './services/FileSystemService';
import type { SLDLTrackLists } from './services/SLDLExtractorService';
import type { DownloadProgress, DownloadJob } from './services/WebSocketService';
import {
  CssBaseline,
  Container,
  AppBar,
  Toolbar,
  Typography,
  Box,
  Paper,
  TextField,
  Button,
  Alert,
  CircularProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  FormControlLabel,
  Checkbox,
  Slider,
  Grid,
  Divider,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  Collapse,
  Tooltip,
} from '@mui/material';
import {
  MusicNote,
  PlaylistPlay,
  Settings,
  Close,
  ExpandMore,
  ExpandLess,
  Person,
  AccessTime,
  QueueMusic,
  ContentCopy,
  FolderOpen,
} from '@mui/icons-material';
import './App.css';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1DB954', // Spotify green
    },
    secondary: {
      main: '#191414', // Spotify black
    },
    background: {
      default: '#f5f5f5',
    },
  },
});

interface Settings {
  // Spotify credentials
  spotifyClientId: string;
  spotifyClientSecret: string;

  // Soulseek credentials
  soulseekUsername: string;
  soulseekPassword: string;

  // Download settings
  downloadDirectory: string;
  enableWav: boolean;
  enableAiff: boolean;
  enableMp3: boolean;
  minimumBitrate: number;
  concurrentDownloads: number;
  listenPort: number;
  enableYtdlpFallback: boolean;

  // SLDL Search Options
  sldlRemoveFt: boolean;
  sldlCustomRegex: string;
  sldlArtistMaybeWrong: boolean;
  sldlDesperate: boolean;
}

// PlaylistInfo and Track interfaces are now imported from SpotifyService

const defaultSettings: Settings = {
  spotifyClientId: '',
  spotifyClientSecret: '',
  soulseekUsername: 'deocsmarco',
  soulseekPassword: '',
  downloadDirectory: 'E:\\slsk\\slsk-batchdl',
  enableWav: true,
  enableAiff: true,
  enableMp3: true,
  minimumBitrate: 320,
  concurrentDownloads: 5,
  listenPort: 59922,
  enableYtdlpFallback: true,
  // SLDL Search Options defaults
  sldlRemoveFt: true,
  sldlCustomRegex: '',
  sldlArtistMaybeWrong: false,
  sldlDesperate: false,
};

function App() {
  const [playlistUrl, setPlaylistUrl] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [settingsTab, setSettingsTab] = useState(0);
  const [settings, setSettings] = useState<Settings>(defaultSettings);
  const [playlistInfo, setPlaylistInfo] = useState<PlaylistInfo | null>(null);
  const [showTracks, setShowTracks] = useState(false);
  const [trackComparison, setTrackComparison] = useState<TrackMatchResult[]>([]);
  const [isCheckingFiles, setIsCheckingFiles] = useState(false);
  const [realDirectoryFiles, setRealDirectoryFiles] = useState<Array<{ name: string; fullPath: string; size: number; format: string }>>([]);
  const [sldlTrackLists, setSldlTrackLists] = useState<SLDLTrackLists | null>(null);

  // New state for download management
  const [isConnected, setIsConnected] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState<DownloadProgress | null>(null);
  const [downloadJobs, setDownloadJobs] = useState<DownloadJob[]>([]);
  const [currentDownloadId, setCurrentDownloadId] = useState<string | null>(null);

  // Simple test to make sure the component renders
  console.log('App component rendering...');

  // Load settings from localStorage on mount and connect to backend
  useEffect(() => {
    const savedSettings = localStorage.getItem('sldl-settings');
    if (savedSettings) {
      try {
        setSettings({ ...defaultSettings, ...JSON.parse(savedSettings) });
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    }

    // Connect to backend WebSocket
    connectToBackend();

    // Cleanup on unmount
    return () => {
      webSocketService.disconnect();
    };
  }, []);

  // Connect to backend
  const connectToBackend = async () => {
    try {
      console.log('🔌 Connecting to SLDL backend...');
      await webSocketService.connect();
      setIsConnected(true);
      setMessage({ type: 'success', text: 'Connected to SLDL backend server' });

      // Set up WebSocket event listeners
      setupWebSocketListeners();

      // Test server connection
      const healthCheck = await apiService.checkHealth();
      if (healthCheck.success) {
        console.log('✅ Backend server is healthy');
      }

    } catch (error) {
      console.error('❌ Failed to connect to backend:', error);
      setIsConnected(false);
      setMessage({
        type: 'error',
        text: 'Failed to connect to backend server. Make sure the server is running on http://localhost:3001'
      });
    }
  };

  // Set up WebSocket event listeners
  const setupWebSocketListeners = () => {
    webSocketService.on('connection:status', (status: string) => {
      setIsConnected(status === 'connected');
      if (status === 'connected') {
        setMessage({ type: 'success', text: 'Connected to backend server' });
      } else if (status === 'disconnected') {
        setMessage({ type: 'error', text: 'Disconnected from backend server' });
      }
    });

    webSocketService.on('download:progress', (progress: DownloadProgress) => {
      console.log('📊 Download progress update:', progress);
      setDownloadProgress(progress);
    });

    webSocketService.on('download:job-update', (job: DownloadJob) => {
      console.log('🔄 Job update:', job);
      setDownloadJobs(prev => {
        const index = prev.findIndex(j => j.id === job.id);
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = job;
          return updated;
        } else {
          return [...prev, job];
        }
      });
    });

    webSocketService.on('download:completed', (data: { requestId: string; summary: any }) => {
      console.log('✅ Download completed:', data);
      setIsDownloading(false);
      setCurrentDownloadId(null);
      setMessage({
        type: 'success',
        text: `Download completed! ${data.summary.successfulTracks}/${data.summary.totalTracks} tracks downloaded successfully.`
      });
    });

    webSocketService.on('download:error', (data: { requestId: string; error: string }) => {
      console.error('❌ Download error:', data);
      setMessage({ type: 'error', text: `Download error: ${data.error}` });
    });
  };

  // Save settings to localStorage
  const saveSettings = (newSettings: Settings) => {
    setSettings(newSettings);
    localStorage.setItem('sldl-settings', JSON.stringify(newSettings));
  };

  // Check if we have valid credentials
  const hasValidCredentials = () => {
    return settings.spotifyClientId.length >= 32 &&
           settings.spotifyClientSecret.length >= 32 &&
           settings.soulseekUsername.length >= 3 &&
           settings.soulseekPassword.length >= 1;
  };

  // Test Spotify credentials
  const testSpotifyCredentials = async () => {
    if (!settings.spotifyClientId || !settings.spotifyClientSecret) {
      return false;
    }

    try {
      return await spotifyService.validateCredentials({
        clientId: settings.spotifyClientId,
        clientSecret: settings.spotifyClientSecret,
      });
    } catch (error) {
      console.error('Credential test failed:', error);
      return false;
    }
  };

  // Helper function to format duration from seconds to MM:SS
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Helper function to format total duration
  const formatTotalDuration = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // Copy file path to clipboard
  const copyPathToClipboard = async (filePath: string) => {
    console.log('Copy button clicked! Path:', filePath);

    try {
      // Check if clipboard API is available
      if (!navigator.clipboard) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = filePath;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        setMessage({ type: 'success', text: 'File path copied to clipboard (fallback method)!' });
      } else {
        await navigator.clipboard.writeText(filePath);
        setMessage({ type: 'success', text: 'File path copied to clipboard!' });
      }

      // Clear the message after 3 seconds
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      setMessage({ type: 'error', text: `Failed to copy path: ${error}` });
      setTimeout(() => setMessage(null), 5000);
    }
  };

  // Open file location (this would need backend support in a real app)
  const openFileLocation = (filePath: string) => {
    console.log('Open folder button clicked! Path:', filePath);

    // In a real application with Electron or backend, this would open the file explorer
    // For now, just show a message and log to console
    setMessage({
      type: 'success',
      text: `File location: ${filePath} (check console for full path)`
    });
    setTimeout(() => setMessage(null), 5000);

    // Also try to open in a new tab (won't work for local files but shows the attempt)
    try {
      // This won't work for local file paths, but demonstrates the functionality
      const fileUrl = `file:///${filePath.replace(/\\/g, '/')}`;
      console.log('Attempting to open:', fileUrl);
      // window.open(fileUrl, '_blank'); // Commented out as it won't work for security reasons
    } catch (error) {
      console.error('Cannot open file location in browser:', error);
    }
  };

  // Scan real directory using File System Access API
  const scanRealDirectory = async () => {
    setIsCheckingFiles(true);
    try {
      const files = await fileSystemService.scanRealDirectory();
      setRealDirectoryFiles(files);

      if (files.length > 0) {
        setMessage({
          type: 'success',
          text: `Scanned real directory: Found ${files.length} audio files`
        });

        // If we have playlist info, re-run the comparison with real files
        if (playlistInfo) {
          await recheckWithRealFiles(files);
        }
      } else {
        setMessage({
          type: 'error',
          text: 'No audio files found in selected directory or access denied'
        });
      }
    } catch (error) {
      console.error('Directory scan error:', error);
      setMessage({
        type: 'error',
        text: 'Failed to scan directory. Make sure you grant permission.'
      });
    } finally {
      setIsCheckingFiles(false);
      setTimeout(() => setMessage(null), 5000);
    }
  };

  // Re-check playlist tracks against real files
  const recheckWithRealFiles = async (realFiles: Array<{ name: string; fullPath: string; size: number; format: string }>) => {
    if (!playlistInfo) return;

    console.log('🔍 Re-checking playlist tracks against real files using SLDL search logic...');
    const results: TrackMatchResult[] = [];

    // Create SLDL options from settings
    const sldlOptions = {
      removeFt: settings.sldlRemoveFt,
      customRegex: settings.sldlCustomRegex || undefined,
      artistMaybeWrong: settings.sldlArtistMaybeWrong,
      desperate: settings.sldlDesperate,
      ytDlpFallback: settings.enableYtdlpFallback,
    };

    for (const track of playlistInfo.tracks) {
      const bestMatch = fileSystemService.findBestMatch(track, realFiles, sldlOptions);
      results.push(bestMatch);
    }

    setTrackComparison(results);
    const summary = fileSystemService.getComparisonSummary(results);

    // Count matches by confidence level
    const perfectMatches = results.filter(r => r.matchScore >= 0.9).length;
    const goodMatches = results.filter(r => r.matchScore >= 0.7 && r.matchScore < 0.9).length;
    const okMatches = results.filter(r => r.matchScore >= 0.5 && r.matchScore < 0.7).length;
    const weakMatches = results.filter(r => r.matchScore >= 0.3 && r.matchScore < 0.5).length;

    setMessage({
      type: 'success',
      text: `Real directory scan complete: ${summary.existing} matches found (${perfectMatches} perfect, ${goodMatches} high confidence, ${okMatches} medium, ${weakMatches} low)`
    });
  };

  // Handle download start
  const handleStartDownload = async () => {
    if (!playlistInfo || !isConnected) {
      setMessage({ type: 'error', text: 'Cannot start download: playlist not validated or backend not connected' });
      return;
    }

    if (!hasValidCredentials()) {
      setMessage({ type: 'error', text: 'Cannot start download: credentials not configured' });
      return;
    }

    try {
      console.log('🎵 Starting download for playlist:', playlistInfo.name);

      const downloadRequest = {
        id: `download-${Date.now()}`,
        playlistUrl,
        config: settings,
        tracks: playlistInfo.tracks,
        createdAt: new Date()
      };

      setIsDownloading(true);
      setCurrentDownloadId(downloadRequest.id);
      setDownloadJobs([]);
      setDownloadProgress(null);

      webSocketService.startDownload(downloadRequest);

      setMessage({
        type: 'success',
        text: `Starting download of ${playlistInfo.tracks.length} tracks...`
      });

    } catch (error) {
      console.error('❌ Failed to start download:', error);
      setIsDownloading(false);
      setCurrentDownloadId(null);
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Failed to start download'
      });
    }
  };

  // Handle download pause
  const handlePauseDownload = () => {
    if (currentDownloadId && isConnected) {
      webSocketService.pauseDownload(currentDownloadId);
      setMessage({ type: 'success', text: 'Pausing download...' });
    }
  };

  // Handle download resume
  const handleResumeDownload = () => {
    if (currentDownloadId && isConnected) {
      webSocketService.resumeDownload(currentDownloadId);
      setMessage({ type: 'success', text: 'Resuming download...' });
    }
  };

  // Handle download cancel
  const handleCancelDownload = () => {
    if (currentDownloadId && isConnected) {
      webSocketService.cancelDownload(currentDownloadId);
      setIsDownloading(false);
      setCurrentDownloadId(null);
      setDownloadJobs([]);
      setDownloadProgress(null);
      setMessage({ type: 'success', text: 'Download cancelled' });
    }
  };

  // Handle retry failed tracks
  const handleRetryFailed = () => {
    if (currentDownloadId && isConnected) {
      const failedTrackIds = downloadJobs
        .filter(job => job.status === 'failed')
        .map(job => job.track.id);

      if (failedTrackIds.length > 0) {
        webSocketService.retryDownload(currentDownloadId, failedTrackIds);
        setMessage({ type: 'success', text: `Retrying ${failedTrackIds.length} failed tracks...` });
      }
    }
  };

  const handleValidate = async () => {
    if (!playlistUrl.trim()) {
      setMessage({ type: 'error', text: 'Please enter a Spotify playlist URL' });
      return;
    }

    if (!hasValidCredentials()) {
      setMessage({ type: 'error', text: 'Please configure your credentials in Settings first' });
      return;
    }

    setIsValidating(true);
    setMessage(null);

    try {
      console.log('🎵 SLDL GUI: Validating input using SLDL extractor logic...');
      console.log('Input:', playlistUrl);

      // First, detect what type of input this is using SLDL logic
      const inputType = sldlExtractorService.detectInputType(playlistUrl.trim());
      console.log('🔍 SLDL would detect this as:', inputType);

      // Extract tracks using SLDL's extraction logic
      const sldlConfig = {
        spotifyClientId: settings.spotifyClientId,
        spotifyClientSecret: settings.spotifyClientSecret,
      };

      const sldlResult = await sldlExtractorService.extractTracks(
        playlistUrl.trim(),
        Number.MAX_SAFE_INTEGER, // maxTracks
        0, // offset
        false, // reverse
        sldlConfig
      );

      console.log('✅ SLDL extraction result:', sldlResult);
      setSldlTrackLists(sldlResult);

      // Convert SLDL result to our PlaylistInfo format for compatibility
      if (sldlResult.lists.length === 0) {
        throw new Error('No tracks found in input');
      }

      const firstList = sldlResult.lists[0];
      const playlistData: PlaylistInfo = {
        id: firstList.source.url,
        name: firstList.source.itemName || 'Extracted Playlist',
        description: `Extracted via SLDL ${inputType.extractor} extractor`,
        owner: 'SLDL',
        trackCount: firstList.tracks.length,
        duration: formatTotalDuration(firstList.tracks.reduce((sum, t) => sum + t.length, 0)),
        isPublic: true,
        tracks: firstList.tracks.map(track => ({
          id: track.uri || `track-${track.itemNumber}`,
          name: track.title,
          artists: [track.artist],
          album: track.album,
          duration: formatDuration(track.length),
        })),
      };

      setPlaylistInfo(playlistData);

      // Check local files for comparison
      setIsCheckingFiles(true);
      try {
        const comparisonResults = await fileSystemService.checkLocalFiles(
          playlistData.tracks,
          settings.downloadDirectory,
          playlistData.name
        );
        setTrackComparison(comparisonResults);

        const summary = fileSystemService.getComparisonSummary(comparisonResults);
        setMessage({
          type: 'success',
          text: `Found playlist: "${playlistData.name}" with ${playlistData.trackCount} tracks. ${summary.existing} already exist locally, ${summary.missing} missing.`
        });
      } catch (fileError) {
        console.error('File comparison error:', fileError);
        setMessage({
          type: 'success',
          text: `Found playlist: "${playlistData.name}" with ${playlistData.trackCount} tracks`
        });
      } finally {
        setIsCheckingFiles(false);
      }
    } catch (error) {
      console.error('Spotify API error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to validate playlist. Please check your URL and credentials.';

      setPlaylistInfo(null);
      setMessage({ type: 'error', text: errorMessage });
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />

      {/* Header */}
      <AppBar position="static" color="secondary">
        <Toolbar>
          <MusicNote sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            SLDL GUI - Soulseek Batch Downloader
          </Typography>
          <IconButton
            color="inherit"
            onClick={() => setSettingsOpen(true)}
            title="Settings"
          >
            <Settings />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Paper elevation={2} sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <PlaylistPlay sx={{ mr: 1, color: 'primary.main', fontSize: 32 }} />
            <Typography variant="h4" component="h1" fontWeight="bold">
              Download Spotify Playlist
            </Typography>
          </Box>

          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Enter a Spotify playlist URL to download music via Soulseek
          </Typography>

          {/* URL Input */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <TextField
              fullWidth
              label="Spotify Playlist URL"
              placeholder="https://open.spotify.com/playlist/..."
              value={playlistUrl}
              onChange={(e) => {
                setPlaylistUrl(e.target.value);
                setMessage(null);
                setPlaylistInfo(null);
                setShowTracks(false);
              }}
              variant="outlined"
            />
            <Button
              variant="contained"
              onClick={handleValidate}
              disabled={isValidating}
              sx={{ minWidth: 120 }}
            >
              {isValidating ? <CircularProgress size={24} /> : 'Validate'}
            </Button>
          </Box>

          {/* Message */}
          {message && (
            <Alert severity={message.type} sx={{ mb: 3 }}>
              {message.text}
            </Alert>
          )}

          {/* Download Controls */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            {!isDownloading ? (
              <Button
                variant="contained"
                size="large"
                fullWidth
                disabled={!playlistInfo || !isConnected || !hasValidCredentials()}
                onClick={handleStartDownload}
                sx={{ py: 1.5, fontSize: '1.1rem', fontWeight: 'bold' }}
              >
                🎵 START DOWNLOAD
              </Button>
            ) : (
              <>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={handlePauseDownload}
                  disabled={!isConnected}
                  sx={{ flex: 1 }}
                >
                  ⏸️ PAUSE
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={handleResumeDownload}
                  disabled={!isConnected}
                  sx={{ flex: 1 }}
                >
                  ▶️ RESUME
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  size="large"
                  onClick={handleCancelDownload}
                  disabled={!isConnected}
                  sx={{ flex: 1 }}
                >
                  ⏹️ CANCEL
                </Button>
              </>
            )}
          </Box>

          {/* Download Progress */}
          {downloadProgress && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Download Progress
                </Typography>

                <LinearProgress
                  variant="determinate"
                  value={downloadProgress.overallProgress}
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">
                    {downloadProgress.completedTracks} / {downloadProgress.totalTracks} tracks completed
                  </Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {downloadProgress.overallProgress.toFixed(1)}%
                  </Typography>
                </Box>

                {downloadProgress.currentTrack && (
                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>
                    <Typography variant="subtitle2" fontWeight="bold">
                      Currently downloading:
                    </Typography>
                    <Typography variant="body2">
                      {downloadProgress.currentTrack.artist} - {downloadProgress.currentTrack.name}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={downloadProgress.currentTrack.progress}
                      sx={{ mt: 1, height: 4, borderRadius: 2 }}
                    />
                  </Box>
                )}

                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip
                    label={`✅ ${downloadProgress.completedTracks} Completed`}
                    color="success"
                    size="small"
                  />
                  <Chip
                    label={`❌ ${downloadProgress.failedTracks} Failed`}
                    color="error"
                    size="small"
                  />
                  {downloadProgress.estimatedTimeRemaining && (
                    <Chip
                      label={`⏱️ ${Math.round(downloadProgress.estimatedTimeRemaining / 60)}m remaining`}
                      color="info"
                      size="small"
                    />
                  )}
                  {downloadProgress.downloadSpeed && (
                    <Chip
                      label={`📊 ${(downloadProgress.downloadSpeed / 1024 / 1024).toFixed(1)} MB/s`}
                      color="primary"
                      size="small"
                    />
                  )}
                </Box>

                {downloadProgress.failedTracks > 0 && (
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={handleRetryFailed}
                    sx={{ mt: 2 }}
                  >
                    🔄 Retry Failed Tracks
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          {/* Playlist Results */}
          {playlistInfo && (
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <PlaylistPlay color="primary" sx={{ fontSize: 32 }} />
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" fontWeight="bold">
                      {playlistInfo.name}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                      <Chip
                        icon={<Person />}
                        label={playlistInfo.owner}
                        size="small"
                        variant="outlined"
                      />
                      <Chip
                        icon={<QueueMusic />}
                        label={`${playlistInfo.trackCount} tracks`}
                        size="small"
                        variant="outlined"
                      />
                      <Chip
                        icon={<AccessTime />}
                        label={playlistInfo.duration}
                        size="small"
                        variant="outlined"
                      />
                      <Chip
                        label={playlistInfo.isPublic ? 'Public' : 'Private'}
                        size="small"
                        color={playlistInfo.isPublic ? 'success' : 'warning'}
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                </Box>

                {playlistInfo.description && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {playlistInfo.description}
                  </Typography>
                )}

                {/* Comparison Summary */}
                {trackComparison.length > 0 && (
                  <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                    {(() => {
                      const summary = fileSystemService.getComparisonSummary(trackComparison);

                      // Calculate match quality distribution
                      const perfectMatches = trackComparison.filter(r => r.matchScore >= 0.9).length;
                      const highMatches = trackComparison.filter(r => r.matchScore >= 0.7 && r.matchScore < 0.9).length;
                      const mediumMatches = trackComparison.filter(r => r.matchScore >= 0.5 && r.matchScore < 0.7).length;
                      const lowMatches = trackComparison.filter(r => r.matchScore >= 0.3 && r.matchScore < 0.5).length;

                      return (
                        <Box>
                          <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                            Local File Comparison - Match Quality Analysis
                          </Typography>

                          {/* Overall Stats */}
                          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                            <Chip
                              label={`${summary.existing} Found`}
                              color="success"
                              size="small"
                            />
                            <Chip
                              label={`${summary.missing} Missing`}
                              color="warning"
                              size="small"
                            />
                            <Chip
                              label={`${fileSystemService.formatFileSize(summary.existingSize)} Saved`}
                              color="info"
                              size="small"
                            />
                          </Box>

                          {/* Match Quality Breakdown */}
                          {summary.existing > 0 && (
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                                Match Quality Distribution:
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                {perfectMatches > 0 && (
                                  <Chip
                                    label={`${perfectMatches} Perfect`}
                                    size="small"
                                    sx={{ backgroundColor: '#4caf50', color: 'white' }}
                                  />
                                )}
                                {highMatches > 0 && (
                                  <Chip
                                    label={`${highMatches} High`}
                                    size="small"
                                    sx={{ backgroundColor: '#8bc34a', color: 'white' }}
                                  />
                                )}
                                {mediumMatches > 0 && (
                                  <Chip
                                    label={`${mediumMatches} Medium`}
                                    size="small"
                                    sx={{ backgroundColor: '#ff9800', color: 'white' }}
                                  />
                                )}
                                {lowMatches > 0 && (
                                  <Chip
                                    label={`${lowMatches} Low`}
                                    size="small"
                                    sx={{ backgroundColor: '#ff5722', color: 'white' }}
                                  />
                                )}
                              </Box>
                            </Box>
                          )}

                          {/* Format Distribution */}
                          {Object.keys(summary.formats).length > 0 && (
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                              Formats: {Object.entries(summary.formats).map(([format, count]) => `${count} ${format}`).join(', ')}
                            </Typography>
                          )}
                        </Box>
                      );
                    })()}
                  </Box>
                )}

                {/* Track List Toggle */}
                <Button
                  onClick={() => setShowTracks(!showTracks)}
                  startIcon={showTracks ? <ExpandLess /> : <ExpandMore />}
                  sx={{ mb: 2 }}
                >
                  {showTracks ? 'Hide' : 'Show'} Track List & Local Comparison
                </Button>

                {/* Track List with Local Comparison */}
                <Collapse in={showTracks}>
                  {isCheckingFiles && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                      <CircularProgress size={20} />
                      <Typography variant="body2" color="text.secondary">
                        Checking local files...
                      </Typography>
                    </Box>
                  )}

                  <Card variant="outlined">
                    <List dense>
                      {playlistInfo.tracks.map((track, index) => {
                        // Find comparison result for this track
                        const comparisonResult = trackComparison.find(r => r.trackId === track.id);
                        const isLocallyAvailable = comparisonResult?.localInfo.exists || false;
                        const localInfo = comparisonResult?.localInfo;
                        const matchScore = comparisonResult?.matchScore || 0;
                        const confidence = fileSystemService.getMatchConfidence(matchScore);

                        return (
                          <ListItem key={track.id} divider={index < playlistInfo.tracks.length - 1}>
                            <ListItemIcon>
                              <Typography variant="body2" color="text.secondary" sx={{ minWidth: 30 }}>
                                {index + 1}
                              </Typography>
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography variant="body1" fontWeight="medium">
                                    {track.name}
                                  </Typography>
                                  <Chip
                                    label={isLocallyAvailable ? confidence.level : 'MISSING'}
                                    size="small"
                                    sx={{
                                      ml: 1,
                                      backgroundColor: isLocallyAvailable ? confidence.color : '#ff9800',
                                      color: 'white',
                                      fontWeight: 'bold'
                                    }}
                                  />
                                  {isLocallyAvailable && (
                                    <Chip
                                      label={`${(matchScore * 100).toFixed(0)}%`}
                                      size="small"
                                      variant="outlined"
                                      sx={{
                                        borderColor: confidence.color,
                                        color: confidence.color
                                      }}
                                    />
                                  )}
                                  {isLocallyAvailable && localInfo?.format && (
                                    <Chip
                                      label={localInfo.format}
                                      size="small"
                                      color="info"
                                      variant="outlined"
                                    />
                                  )}
                                </Box>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    {track.artists.join(', ')} • {track.album}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {track.duration}
                                  </Typography>
                                  {isLocallyAvailable && localInfo && (
                                    <Box sx={{ mt: 0.5 }}>
                                      <Typography variant="caption" sx={{ color: confidence.color, fontWeight: 'medium' }}>
                                        ✓ {confidence.description}: {localInfo.fileName} ({fileSystemService.formatFileSize(localInfo.fileSize || 0)})
                                      </Typography>
                                      <Typography variant="caption" sx={{ color: '#888', fontSize: '0.65rem', display: 'block', mt: 0.5 }}>
                                        🔍 SLDL Strategy: {comparisonResult?.searchStrategy || 'normal'} | Search terms: {comparisonResult?.sldlSearchTerms?.slice(0, 3).join(', ') || 'N/A'}{comparisonResult?.sldlSearchTerms && comparisonResult.sldlSearchTerms.length > 3 ? '...' : ''}
                                      </Typography>
                                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                                        <Typography variant="caption" sx={{ color: '#666', fontFamily: 'monospace', fontSize: '0.7rem', flexGrow: 1 }}>
                                          📁 {localInfo.filePath}
                                        </Typography>
                                        <Tooltip title="Copy path to clipboard">
                                          <IconButton
                                            size="small"
                                            onClick={(e) => {
                                              e.preventDefault();
                                              e.stopPropagation();
                                              console.log('Copy button clicked for:', localInfo.filePath);
                                              copyPathToClipboard(localInfo.filePath || '');
                                            }}
                                            sx={{
                                              p: 0.5,
                                              '&:hover': {
                                                backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                                color: 'primary.main'
                                              }
                                            }}
                                          >
                                            <ContentCopy sx={{ fontSize: 16 }} />
                                          </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Open file location">
                                          <IconButton
                                            size="small"
                                            onClick={(e) => {
                                              e.preventDefault();
                                              e.stopPropagation();
                                              console.log('Open folder button clicked for:', localInfo.filePath);
                                              openFileLocation(localInfo.filePath || '');
                                            }}
                                            sx={{
                                              p: 0.5,
                                              '&:hover': {
                                                backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                                color: 'primary.main'
                                              }
                                            }}
                                          >
                                            <FolderOpen sx={{ fontSize: 16 }} />
                                          </IconButton>
                                        </Tooltip>
                                      </Box>
                                    </Box>
                                  )}
                                </Box>
                              }
                            />
                          </ListItem>
                        );
                      })}
                      {playlistInfo.trackCount > playlistInfo.tracks.length && (
                        <ListItem>
                          <ListItemText
                            primary={
                              <Typography variant="body2" color="text.secondary" textAlign="center">
                                ... and {playlistInfo.trackCount - playlistInfo.tracks.length} more tracks
                              </Typography>
                            }
                          />
                        </ListItem>
                      )}
                    </List>
                  </Card>
                </Collapse>
              </CardContent>
            </Card>
          )}

          {/* SLDL Extraction Info */}
          {sldlTrackLists && (
            <Card sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  🔧 SLDL Extraction Analysis
                </Typography>

                {sldlTrackLists.lists.map((list, index) => (
                  <Box key={index} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
                      <Chip
                        label={`Source: ${list.source.type}`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Chip
                        label={`${list.tracks.length} tracks`}
                        size="small"
                        color="info"
                        variant="outlined"
                      />
                      <Chip
                        label={list.enablesIndexByDefault ? 'Indexed' : 'Not Indexed'}
                        size="small"
                        color={list.enablesIndexByDefault ? 'success' : 'warning'}
                        variant="outlined"
                      />
                      <Chip
                        label={list.sourceCanBeSkipped ? 'Skippable' : 'Required'}
                        size="small"
                        color={list.sourceCanBeSkipped ? 'default' : 'error'}
                        variant="outlined"
                      />
                    </Box>

                    <Typography variant="body2" color="text.secondary">
                      <strong>Source URL:</strong> {list.source.url}
                    </Typography>

                    {list.source.itemName && (
                      <Typography variant="body2" color="text.secondary">
                        <strong>Item Name:</strong> {list.source.itemName}
                      </Typography>
                    )}

                    {list.extractorCond && (
                      <Typography variant="body2" color="text.secondary">
                        <strong>Extractor Condition:</strong> {list.extractorCond}
                      </Typography>
                    )}

                    {list.extractorPrefCond && (
                      <Typography variant="body2" color="text.secondary">
                        <strong>Preference Condition:</strong> {list.extractorPrefCond}
                      </Typography>
                    )}
                  </Box>
                ))}

                <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                    💡 This shows exactly how SLDL would process your input:
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                    • <strong>Source Type:</strong> How SLDL categorizes the input
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                    • <strong>Indexed:</strong> Whether SLDL enables indexing by default
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                    • <strong>Skippable:</strong> Whether source can be skipped if already processed
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Status */}
          <Box sx={{ mt: 4, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              <strong>Status:</strong> {hasValidCredentials() ? 'Ready to download' : 'Configure credentials in Settings'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>CLI Core:</strong> Connected to existing SLDL engine
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Credentials:</strong> {hasValidCredentials() ? '✅ Configured' : '❌ Not configured'}
            </Typography>
          </Box>

          {/* Real Directory Scanning */}
          <Box sx={{ mt: 2, p: 2, bgcolor: 'warning.light', borderRadius: 1, opacity: 0.9 }}>
            <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
              🔍 Real Directory Scanning
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 2 }}>
              Currently using simulated file data. Click below to scan your actual download directory:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Button
                size="small"
                variant="contained"
                color="warning"
                startIcon={<FolderOpen />}
                onClick={scanRealDirectory}
                disabled={isCheckingFiles || !fileSystemService.supportsRealDirectoryScanning()}
              >
                {isCheckingFiles ? 'Scanning...' : 'Scan Real Directory'}
              </Button>
              {!fileSystemService.supportsRealDirectoryScanning() && (
                <Typography variant="caption" color="error">
                  (Not supported in this browser)
                </Typography>
              )}
              {realDirectoryFiles.length > 0 && (
                <Typography variant="caption" color="success.main">
                  ✓ {realDirectoryFiles.length} files scanned
                </Typography>
              )}
            </Box>
          </Box>

          {/* Test Buttons for Debugging */}
          <Box sx={{ mt: 2, p: 2, bgcolor: 'info.light', borderRadius: 1, opacity: 0.7 }}>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              Test File Actions:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                size="small"
                variant="outlined"
                startIcon={<ContentCopy />}
                onClick={() => copyPathToClipboard('C:\\Music\\test-song.mp3')}
              >
                Test Copy
              </Button>
              <Button
                size="small"
                variant="outlined"
                startIcon={<FolderOpen />}
                onClick={() => openFileLocation('C:\\Music\\test-song.mp3')}
              >
                Test Open
              </Button>
            </Box>
          </Box>
        </Paper>
      </Container>

      {/* Settings Dialog */}
      <Dialog
        open={settingsOpen}
        onClose={() => setSettingsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">Settings</Typography>
            <IconButton onClick={() => setSettingsOpen(false)}>
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Tabs
            value={settingsTab}
            onChange={(_, newValue) => setSettingsTab(newValue)}
            sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}
          >
            <Tab label="Authentication" />
            <Tab label="Downloads" />
            <Tab label="SLDL Search" />
          </Tabs>

          {/* Authentication Tab */}
          {settingsTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Spotify Credentials
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Get your credentials from the Spotify Developer Dashboard
              </Typography>

              <TextField
                fullWidth
                label="Client ID"
                value={settings.spotifyClientId}
                onChange={(e) => setSettings({ ...settings, spotifyClientId: e.target.value })}
                sx={{ mb: 2 }}
                placeholder="Your Spotify Client ID"
              />

              <TextField
                fullWidth
                label="Client Secret"
                type="password"
                value={settings.spotifyClientSecret}
                onChange={(e) => setSettings({ ...settings, spotifyClientSecret: e.target.value })}
                sx={{ mb: 2 }}
                placeholder="Your Spotify Client Secret"
              />

              <Button
                variant="outlined"
                onClick={() => {
                  testSpotifyCredentials().then(isValid => {
                    setMessage({
                      type: isValid ? 'success' : 'error',
                      text: isValid ? 'Spotify credentials are valid!' : 'Invalid Spotify credentials. Please check your Client ID and Secret.'
                    });
                  }).catch(error => {
                    console.error('Credential test error:', error);
                    setMessage({
                      type: 'error',
                      text: 'Failed to test credentials. Please try again.'
                    });
                  });
                }}
                disabled={!settings.spotifyClientId || !settings.spotifyClientSecret}
                sx={{ mb: 3 }}
              >
                Test Spotify Credentials
              </Button>

              <Divider sx={{ my: 3 }} />

              <Typography variant="h6" gutterBottom>
                Soulseek Credentials
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Your Soulseek username and password
              </Typography>

              <TextField
                fullWidth
                label="Username"
                value={settings.soulseekUsername}
                onChange={(e) => setSettings({ ...settings, soulseekUsername: e.target.value })}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Password"
                type="password"
                value={settings.soulseekPassword}
                onChange={(e) => setSettings({ ...settings, soulseekPassword: e.target.value })}
              />
            </Box>
          )}

          {/* Downloads Tab */}
          {settingsTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Download Directory
              </Typography>
              <TextField
                fullWidth
                label="Download Directory"
                value={settings.downloadDirectory}
                onChange={(e) => setSettings({ ...settings, downloadDirectory: e.target.value })}
                sx={{ mb: 3 }}
              />

              <Typography variant="h6" gutterBottom>
                Audio Formats
              </Typography>
              <Box sx={{ mb: 3 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={settings.enableWav}
                      onChange={(e) => setSettings({ ...settings, enableWav: e.target.checked })}
                    />
                  }
                  label="WAV"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={settings.enableAiff}
                      onChange={(e) => setSettings({ ...settings, enableAiff: e.target.checked })}
                    />
                  }
                  label="AIFF"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={settings.enableMp3}
                      onChange={(e) => setSettings({ ...settings, enableMp3: e.target.checked })}
                    />
                  }
                  label="MP3"
                />
              </Box>

              <Typography variant="h6" gutterBottom>
                Minimum Bitrate: {settings.minimumBitrate} kbps
              </Typography>
              <Slider
                value={settings.minimumBitrate}
                onChange={(_, value) => setSettings({ ...settings, minimumBitrate: value as number })}
                min={128}
                max={320}
                step={32}
                marks={[
                  { value: 128, label: '128' },
                  { value: 192, label: '192' },
                  { value: 256, label: '256' },
                  { value: 320, label: '320' },
                ]}
                sx={{ mb: 3 }}
              />

              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Concurrent Downloads"
                    type="number"
                    value={settings.concurrentDownloads}
                    onChange={(e) => setSettings({ ...settings, concurrentDownloads: parseInt(e.target.value) || 5 })}
                    inputProps={{ min: 1, max: 20 }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Listen Port"
                    type="number"
                    value={settings.listenPort}
                    onChange={(e) => setSettings({ ...settings, listenPort: parseInt(e.target.value) || 59922 })}
                    inputProps={{ min: 1024, max: 65535 }}
                  />
                </Grid>
              </Grid>

              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings.enableYtdlpFallback}
                    onChange={(e) => setSettings({ ...settings, enableYtdlpFallback: e.target.checked })}
                  />
                }
                label="Enable yt-dlp fallback for tracks not found on Soulseek"
              />
            </Box>
          )}

          {/* SLDL Search Tab */}
          {settingsTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                SLDL Search Options
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Configure how SLDL searches for tracks. These options affect both local file matching and actual downloads.
              </Typography>

              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings.sldlRemoveFt}
                    onChange={(e) => setSettings({ ...settings, sldlRemoveFt: e.target.checked })}
                  />
                }
                label="Remove 'feat.' and everything after (--remove-ft)"
                sx={{ mb: 2, display: 'block' }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ ml: 4, mb: 2, display: 'block' }}>
                Removes featuring artists from search terms. "Song (feat. Artist)" becomes "Song"
              </Typography>

              <TextField
                fullWidth
                label="Custom Regex Pattern (--regex)"
                value={settings.sldlCustomRegex}
                onChange={(e) => setSettings({ ...settings, sldlCustomRegex: e.target.value })}
                placeholder="e.g., \\(.*?\\) to remove text in parentheses"
                sx={{ mb: 1 }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ mb: 3, display: 'block' }}>
                Remove custom patterns from track titles and artist names. Use semicolon for replacement: "pattern;replacement"
              </Typography>

              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings.sldlArtistMaybeWrong}
                    onChange={(e) => setSettings({ ...settings, sldlArtistMaybeWrong: e.target.checked })}
                  />
                }
                label="Artist maybe wrong (--artist-maybe-wrong)"
                sx={{ mb: 2, display: 'block' }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ ml: 4, mb: 2, display: 'block' }}>
                Also searches without artist name. Useful for SoundCloud uploads where "artist" might be uploader.
              </Typography>

              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings.sldlDesperate}
                    onChange={(e) => setSettings({ ...settings, sldlDesperate: e.target.checked })}
                  />
                }
                label="Desperate search (--desperate)"
                sx={{ mb: 2, display: 'block' }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ ml: 4, mb: 3, display: 'block' }}>
                Searches for artist, album, and title separately. Slower but finds more matches.
              </Typography>

              <Box sx={{ p: 2, bgcolor: 'info.light', borderRadius: 1, opacity: 0.8 }}>
                <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                  💡 Search Strategy Tips
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                  • Start with default settings (remove-ft enabled)
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                  • Enable "artist maybe wrong" for playlists from SoundCloud/YouTube
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                  • Use "desperate" mode if many tracks aren't found
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                  • Custom regex helps with specific naming patterns in your library
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              saveSettings(settings);
              setSettingsOpen(false);
            }}
          >
            Save Settings
          </Button>
        </DialogActions>
      </Dialog>
    </ThemeProvider>
  );

}

export default App;
