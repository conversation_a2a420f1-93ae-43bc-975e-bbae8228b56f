import { io, Socket } from 'socket.io-client';

export interface DownloadProgress {
  requestId: string;
  totalTracks: number;
  completedTracks: number;
  failedTracks: number;
  currentTrack?: {
    name: string;
    artist: string;
    progress: number;
    status: string;
  };
  overallProgress: number;
  estimatedTimeRemaining?: number;
  downloadSpeed?: number;
}

export interface DownloadJob {
  id: string;
  requestId: string;
  track: {
    id: string;
    name: string;
    artists: string[];
    album: string;
  };
  status: string;
  progress: number;
  error?: string;
  filePath?: string;
  fileSize?: number;
  startedAt?: Date;
  completedAt?: Date;
  retryCount: number;
  maxRetries: number;
}

export interface DownloadSummary {
  requestId: string;
  totalTracks: number;
  successfulTracks: number;
  failedTracks: number;
  skippedTracks: number;
  totalSize: number;
  totalDuration: number;
  startedAt: Date;
  completedAt: Date;
  errors: Array<{
    trackId: string;
    trackName: string;
    error: string;
  }>;
}

export type WebSocketEventCallback<T = any> = (data: T) => void;

export class WebSocketService {
  private socket: Socket | null = null;
  private serverUrl: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventListeners = new Map<string, WebSocketEventCallback[]>();

  constructor(serverUrl = 'http://localhost:3001') {
    this.serverUrl = serverUrl;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      console.log('🔌 Connecting to SLDL backend server...');

      this.socket = io(this.serverUrl, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true
      });

      this.socket.on('connect', () => {
        console.log('✅ Connected to SLDL backend server');
        this.reconnectAttempts = 0;
        this.setupEventHandlers();
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ Failed to connect to SLDL backend server:', error);
        this.handleReconnect();
        reject(error);
      });

      this.socket.on('disconnect', (reason) => {
        console.warn('🔌 Disconnected from SLDL backend server:', reason);
        this.notifyListeners('connection:status', 'disconnected');
        
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, don't reconnect automatically
          return;
        }
        
        this.handleReconnect();
      });

      this.socket.on('reconnect', () => {
        console.log('🔄 Reconnected to SLDL backend server');
        this.reconnectAttempts = 0;
        this.notifyListeners('connection:status', 'connected');
      });

      this.socket.on('reconnect_error', (error) => {
        console.error('🔄 Reconnection failed:', error);
      });

      this.socket.on('reconnect_failed', () => {
        console.error('🔄 Failed to reconnect after maximum attempts');
        this.notifyListeners('connection:status', 'failed');
      });
    });
  }

  disconnect(): void {
    if (this.socket) {
      console.log('🔌 Disconnecting from SLDL backend server');
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventListeners.clear();
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Download control methods
  startDownload(request: any): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server');
    }
    console.log('🎵 Starting download:', request.id);
    this.socket.emit('download:start', request);
  }

  pauseDownload(requestId: string): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server');
    }
    console.log('⏸️ Pausing download:', requestId);
    this.socket.emit('download:pause', requestId);
  }

  resumeDownload(requestId: string): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server');
    }
    console.log('▶️ Resuming download:', requestId);
    this.socket.emit('download:resume', requestId);
  }

  cancelDownload(requestId: string): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server');
    }
    console.log('⏹️ Cancelling download:', requestId);
    this.socket.emit('download:cancel', requestId);
  }

  retryDownload(requestId: string, trackIds?: string[]): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server');
    }
    console.log('🔄 Retrying download:', requestId, trackIds);
    this.socket.emit('download:retry', requestId, trackIds);
  }

  // Event listener management
  on<T = any>(event: string, callback: WebSocketEventCallback<T>): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback?: WebSocketEventCallback): void {
    if (!this.eventListeners.has(event)) return;

    if (callback) {
      const listeners = this.eventListeners.get(event)!;
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    } else {
      this.eventListeners.delete(event);
    }
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Connection status
    this.socket.on('connection:status', (status: string) => {
      console.log('📡 Connection status:', status);
      this.notifyListeners('connection:status', status);
    });

    // Download progress updates
    this.socket.on('download:progress', (progress: DownloadProgress) => {
      console.log('📊 Download progress:', progress);
      this.notifyListeners('download:progress', progress);
    });

    // Individual job updates
    this.socket.on('download:job-update', (job: DownloadJob) => {
      console.log('🔄 Job update:', job);
      this.notifyListeners('download:job-update', job);
    });

    // Download completion
    this.socket.on('download:completed', (requestId: string, summary: DownloadSummary) => {
      console.log('✅ Download completed:', requestId, summary);
      this.notifyListeners('download:completed', { requestId, summary });
    });

    // Download errors
    this.socket.on('download:error', (requestId: string, error: string) => {
      console.error('❌ Download error:', requestId, error);
      this.notifyListeners('download:error', { requestId, error });
    });
  }

  private notifyListeners<T = any>(event: string, data: T): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🔄 Maximum reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);

    setTimeout(() => {
      if (!this.socket?.connected) {
        this.connect().catch(error => {
          console.error('🔄 Reconnection attempt failed:', error);
        });
      }
    }, delay);
  }
}

// Create singleton instance
export const webSocketService = new WebSocketService();
