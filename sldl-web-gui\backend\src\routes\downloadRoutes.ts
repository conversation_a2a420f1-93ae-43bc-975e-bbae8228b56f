import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import type { DownloadManager } from '../services/DownloadManager.js';
import type { DownloadRequest, ApiResponse, DownloadConfig, Track } from '../types/index.js';

export function downloadRoutes(downloadManager: DownloadManager): Router {
  const router = Router();

  // Start a new download
  router.post('/start', async (req, res) => {
    try {
      const { playlistUrl, config, tracks } = req.body;

      // Validate required fields
      if (!playlistUrl || !config || !tracks || !Array.isArray(tracks)) {
        return res.status(400).json({
          success: false,
          error: 'Missing required fields: playlistUrl, config, tracks'
        } as ApiResponse);
      }

      // Validate config
      const requiredConfigFields = [
        'spotifyClientId', 'spotifyClientSecret',
        'soulseekUsername', 'soulseekPassword',
        'downloadDirectory'
      ];

      for (const field of requiredConfigFields) {
        if (!config[field]) {
          return res.status(400).json({
            success: false,
            error: `Missing required config field: ${field}`
          } as ApiResponse);
        }
      }

      // Create download request
      const downloadRequest: DownloadRequest = {
        id: uuidv4(),
        playlistUrl,
        config: config as DownloadConfig,
        tracks: tracks as Track[],
        createdAt: new Date()
      };

      // Start download
      const result = await downloadManager.startDownload(downloadRequest);

      res.json({
        success: true,
        data: {
          requestId: downloadRequest.id,
          message: 'Download started successfully',
          totalTracks: tracks.length
        }
      } as ApiResponse);

    } catch (error) {
      console.error('Download start error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to start download'
      } as ApiResponse);
    }
  });

  // Pause a download
  router.post('/pause/:requestId', async (req, res) => {
    try {
      const { requestId } = req.params;
      await downloadManager.pauseDownload(requestId);

      res.json({
        success: true,
        message: 'Download paused successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Download pause error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to pause download'
      } as ApiResponse);
    }
  });

  // Resume a download
  router.post('/resume/:requestId', async (req, res) => {
    try {
      const { requestId } = req.params;
      await downloadManager.resumeDownload(requestId);

      res.json({
        success: true,
        message: 'Download resumed successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Download resume error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to resume download'
      } as ApiResponse);
    }
  });

  // Cancel a download
  router.post('/cancel/:requestId', async (req, res) => {
    try {
      const { requestId } = req.params;
      await downloadManager.cancelDownload(requestId);

      res.json({
        success: true,
        message: 'Download cancelled successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Download cancel error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to cancel download'
      } as ApiResponse);
    }
  });

  // Retry failed tracks
  router.post('/retry/:requestId', async (req, res) => {
    try {
      const { requestId } = req.params;
      const { trackIds } = req.body; // Optional: specific track IDs to retry

      await downloadManager.retryDownload(requestId, trackIds);

      res.json({
        success: true,
        message: 'Retry initiated successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Download retry error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to retry download'
      } as ApiResponse);
    }
  });

  // Get download status
  router.get('/status/:requestId', async (req, res) => {
    try {
      const { requestId } = req.params;
      const status = await downloadManager.getDownloadStatus(requestId);

      if (!status) {
        return res.status(404).json({
          success: false,
          error: 'Download request not found'
        } as ApiResponse);
      }

      res.json({
        success: true,
        data: status
      } as ApiResponse);

    } catch (error) {
      console.error('Get download status error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get download status'
      } as ApiResponse);
    }
  });

  // Get all active downloads
  router.get('/active', async (req, res) => {
    try {
      const activeDownloads = await downloadManager.getActiveDownloads();

      res.json({
        success: true,
        data: activeDownloads
      } as ApiResponse);

    } catch (error) {
      console.error('Get active downloads error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get active downloads'
      } as ApiResponse);
    }
  });

  // Get download history
  router.get('/history', async (req, res) => {
    try {
      const { limit = 50, offset = 0 } = req.query;
      const history = await downloadManager.getDownloadHistory(
        parseInt(limit as string),
        parseInt(offset as string)
      );

      res.json({
        success: true,
        data: history
      } as ApiResponse);

    } catch (error) {
      console.error('Get download history error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get download history'
      } as ApiResponse);
    }
  });

  return router;
}
