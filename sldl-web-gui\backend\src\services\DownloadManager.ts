import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { SLDLCliService } from './SLDLCliService.js';
import type {
  DownloadRequest,
  DownloadJob,
  DownloadStatus,
  DownloadProgress,
  DownloadSummary,
  Track,
  SLDLCliOptions
} from '../types/index.js';

export class DownloadManager extends EventEmitter {
  private activeDownloads = new Map<string, DownloadRequest>();
  private downloadJobs = new Map<string, DownloadJob[]>();
  private downloadHistory: DownloadSummary[] = [];
  private maxConcurrentDownloads: number;
  private logs: string[] = [];
  private maxLogs = 1000;
  private sldlCliService: SLDLCliService;

  constructor(maxConcurrentDownloads = 5) {
    super();
    this.maxConcurrentDownloads = maxConcurrentDownloads;
    this.sldlCliService = new SLDLCliService();
    this.setupSLDLCliListeners();
    this.log('DownloadManager initialized');
  }

  private log(message: string): void {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    
    this.logs.push(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  async startDownload(request: DownloadRequest): Promise<void> {
    this.log(`Starting download for request ${request.id} with ${request.tracks.length} tracks`);
    
    // Store the download request
    this.activeDownloads.set(request.id, request);

    // Create download jobs for each track
    const jobs: DownloadJob[] = request.tracks.map(track => ({
      id: uuidv4(),
      requestId: request.id,
      track,
      status: DownloadStatus.QUEUED,
      progress: 0,
      retryCount: 0,
      maxRetries: 3
    }));

    this.downloadJobs.set(request.id, jobs);

    // Emit initial progress
    this.emitProgress(request.id);

    // Start processing jobs
    this.processDownloadQueue(request.id);
  }

  async pauseDownload(requestId: string): Promise<void> {
    this.log(`Pausing download ${requestId}`);
    
    const jobs = this.downloadJobs.get(requestId);
    if (!jobs) {
      throw new Error('Download request not found');
    }

    // Mark queued jobs as paused (we'll implement pause/resume states)
    jobs.forEach(job => {
      if (job.status === DownloadStatus.QUEUED) {
        job.status = DownloadStatus.CANCELLED; // Temporary - we'll add PAUSED status
      }
    });

    this.emitProgress(requestId);
  }

  async resumeDownload(requestId: string): Promise<void> {
    this.log(`Resuming download ${requestId}`);
    
    const jobs = this.downloadJobs.get(requestId);
    if (!jobs) {
      throw new Error('Download request not found');
    }

    // Resume cancelled jobs
    jobs.forEach(job => {
      if (job.status === DownloadStatus.CANCELLED) {
        job.status = DownloadStatus.QUEUED;
      }
    });

    this.emitProgress(requestId);
    this.processDownloadQueue(requestId);
  }

  async cancelDownload(requestId: string): Promise<void> {
    this.log(`Cancelling download ${requestId}`);
    
    const jobs = this.downloadJobs.get(requestId);
    if (!jobs) {
      throw new Error('Download request not found');
    }

    // Cancel all jobs
    jobs.forEach(job => {
      if (job.status !== DownloadStatus.COMPLETED) {
        job.status = DownloadStatus.CANCELLED;
      }
    });

    // Remove from active downloads
    this.activeDownloads.delete(requestId);
    
    this.emitProgress(requestId);
    this.emitCompleted(requestId);
  }

  async retryDownload(requestId: string, trackIds?: string[]): Promise<void> {
    this.log(`Retrying download ${requestId}${trackIds ? ` for tracks: ${trackIds.join(', ')}` : ''}`);
    
    const jobs = this.downloadJobs.get(requestId);
    if (!jobs) {
      throw new Error('Download request not found');
    }

    // Retry specific tracks or all failed tracks
    jobs.forEach(job => {
      const shouldRetry = trackIds 
        ? trackIds.includes(job.track.id)
        : job.status === DownloadStatus.FAILED;

      if (shouldRetry && job.retryCount < job.maxRetries) {
        job.status = DownloadStatus.QUEUED;
        job.retryCount++;
        job.error = undefined;
      }
    });

    this.emitProgress(requestId);
    this.processDownloadQueue(requestId);
  }

  async getDownloadStatus(requestId: string): Promise<DownloadProgress | null> {
    const jobs = this.downloadJobs.get(requestId);
    if (!jobs) {
      return null;
    }

    return this.calculateProgress(requestId, jobs);
  }

  async getActiveDownloads(): Promise<DownloadProgress[]> {
    const activeDownloads: DownloadProgress[] = [];
    
    for (const [requestId, jobs] of this.downloadJobs.entries()) {
      if (this.activeDownloads.has(requestId)) {
        const progress = this.calculateProgress(requestId, jobs);
        activeDownloads.push(progress);
      }
    }

    return activeDownloads;
  }

  async getDownloadHistory(limit = 50, offset = 0): Promise<DownloadSummary[]> {
    return this.downloadHistory
      .slice(offset, offset + limit);
  }

  async getActiveDownloadCount(): Promise<number> {
    return this.activeDownloads.size;
  }

  async getQueuedDownloadCount(): Promise<number> {
    let queuedCount = 0;
    for (const jobs of this.downloadJobs.values()) {
      queuedCount += jobs.filter(job => job.status === DownloadStatus.QUEUED).length;
    }
    return queuedCount;
  }

  async getTotalDownloadCount(): Promise<number> {
    return this.downloadHistory.length + this.activeDownloads.size;
  }

  async getDownloadStats(): Promise<any> {
    const totalDownloads = this.downloadHistory.length;
    const successfulDownloads = this.downloadHistory.filter(d => d.successfulTracks > 0).length;
    const totalTracks = this.downloadHistory.reduce((sum, d) => sum + d.totalTracks, 0);
    const successfulTracks = this.downloadHistory.reduce((sum, d) => sum + d.successfulTracks, 0);
    const totalSize = this.downloadHistory.reduce((sum, d) => sum + d.totalSize, 0);

    return {
      totalDownloads,
      successfulDownloads,
      totalTracks,
      successfulTracks,
      totalSize,
      successRate: totalTracks > 0 ? (successfulTracks / totalTracks) * 100 : 0,
      averageTracksPerDownload: totalDownloads > 0 ? totalTracks / totalDownloads : 0
    };
  }

  async getRecentLogs(lines = 100): Promise<string[]> {
    return this.logs.slice(-lines);
  }

  cleanup(): void {
    this.log('Cleaning up DownloadManager');
    this.sldlCliService.cleanup();
    this.activeDownloads.clear();
    this.downloadJobs.clear();
    this.removeAllListeners();
  }

  private async processDownloadQueue(requestId: string): Promise<void> {
    const jobs = this.downloadJobs.get(requestId);
    if (!jobs) return;

    const queuedJobs = jobs.filter(job => job.status === DownloadStatus.QUEUED);
    const activeJobs = jobs.filter(job => 
      job.status === DownloadStatus.SEARCHING || 
      job.status === DownloadStatus.DOWNLOADING
    );

    // Process jobs up to concurrent limit
    const availableSlots = this.maxConcurrentDownloads - activeJobs.length;
    const jobsToProcess = queuedJobs.slice(0, availableSlots);

    for (const job of jobsToProcess) {
      this.processDownloadJob(job);
    }
  }

  private async processDownloadJob(job: DownloadJob): Promise<void> {
    this.log(`Processing job ${job.id} for track: ${job.track.name} by ${job.track.artists.join(', ')}`);

    try {
      // Update job status
      job.status = DownloadStatus.SEARCHING;
      job.startedAt = new Date();
      this.emitJobUpdate(job);

      // Get the download request to access config
      const request = this.activeDownloads.get(job.requestId);
      if (!request) {
        throw new Error('Download request not found');
      }

      // Build SLDL CLI options from config
      const sldlOptions: SLDLCliOptions = {
        input: '', // Will be created by SLDLCliService
        musicDir: request.config.downloadDirectory,
        username: request.config.soulseekUsername,
        password: request.config.soulseekPassword,
        concurrent: 1, // Process one track at a time per job
        port: request.config.listenPort,
        formats: this.buildFormatsArray(request.config),
        minBitrate: request.config.minimumBitrate,
        removeFt: request.config.sldlRemoveFt,
        customRegex: request.config.sldlCustomRegex || undefined,
        artistMaybeWrong: request.config.sldlArtistMaybeWrong,
        desperate: request.config.sldlDesperate,
        ytDlp: request.config.enableYtdlpFallback,
        maxRetries: job.maxRetries
      };

      // Execute download using SLDL CLI
      await this.sldlCliService.executeDownload(job, sldlOptions);

    } catch (error) {
      this.log(`Job ${job.id} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      job.status = DownloadStatus.FAILED;
      job.error = error instanceof Error ? error.message : 'Unknown error';
      this.emitJobUpdate(job);

      // Check if all jobs are complete even on failure
      this.checkDownloadCompletion(job.requestId);
    }
  }

  private buildFormatsArray(config: any): string[] {
    const formats: string[] = [];
    if (config.enableWav) formats.push('wav');
    if (config.enableAiff) formats.push('aiff');
    if (config.enableMp3) formats.push('mp3');
    return formats;
  }

  private setupSLDLCliListeners(): void {
    this.sldlCliService.on('progress', (progress) => {
      const job = this.findJobById(progress.jobId);
      if (job) {
        job.status = progress.status;
        job.progress = progress.progress;
        if (progress.error) {
          job.error = progress.error;
        }
        if (progress.status === DownloadStatus.COMPLETED) {
          job.completedAt = new Date();
          // Try to determine file path and size
          // This would need to be enhanced based on SLDL CLI output parsing
        }
        this.emitJobUpdate(job);

        // Check completion when job finishes
        if (progress.status === DownloadStatus.COMPLETED ||
            progress.status === DownloadStatus.FAILED) {
          this.checkDownloadCompletion(job.requestId);
        }
      }
    });
  }

  private findJobById(jobId: string): DownloadJob | undefined {
    for (const jobs of this.downloadJobs.values()) {
      const job = jobs.find(j => j.id === jobId);
      if (job) return job;
    }
    return undefined;
  }

  private calculateProgress(requestId: string, jobs: DownloadJob[]): DownloadProgress {
    const totalTracks = jobs.length;
    const completedTracks = jobs.filter(job => job.status === DownloadStatus.COMPLETED).length;
    const failedTracks = jobs.filter(job => job.status === DownloadStatus.FAILED).length;
    
    const currentJob = jobs.find(job => 
      job.status === DownloadStatus.SEARCHING || 
      job.status === DownloadStatus.DOWNLOADING
    );

    const overallProgress = totalTracks > 0 ? (completedTracks / totalTracks) * 100 : 0;

    return {
      requestId,
      totalTracks,
      completedTracks,
      failedTracks,
      currentTrack: currentJob ? {
        name: currentJob.track.name,
        artist: currentJob.track.artists.join(', '),
        progress: currentJob.progress,
        status: currentJob.status
      } : undefined,
      overallProgress,
      estimatedTimeRemaining: this.calculateETA(jobs),
      downloadSpeed: this.calculateDownloadSpeed(jobs)
    };
  }

  private calculateETA(jobs: DownloadJob[]): number | undefined {
    // Simple ETA calculation based on completed jobs
    const completedJobs = jobs.filter(job => job.status === DownloadStatus.COMPLETED);
    const remainingJobs = jobs.filter(job => 
      job.status === DownloadStatus.QUEUED || 
      job.status === DownloadStatus.SEARCHING || 
      job.status === DownloadStatus.DOWNLOADING
    );

    if (completedJobs.length === 0 || remainingJobs.length === 0) {
      return undefined;
    }

    const avgTimePerJob = completedJobs.reduce((sum, job) => {
      if (job.startedAt && job.completedAt) {
        return sum + (job.completedAt.getTime() - job.startedAt.getTime());
      }
      return sum;
    }, 0) / completedJobs.length;

    return (avgTimePerJob * remainingJobs.length) / 1000; // Return in seconds
  }

  private calculateDownloadSpeed(jobs: DownloadJob[]): number | undefined {
    const recentJobs = jobs.filter(job => 
      job.status === DownloadStatus.COMPLETED && 
      job.completedAt && 
      job.startedAt &&
      (Date.now() - job.completedAt.getTime()) < 60000 // Last minute
    );

    if (recentJobs.length === 0) return undefined;

    const totalSize = recentJobs.reduce((sum, job) => sum + (job.fileSize || 0), 0);
    const totalTime = recentJobs.reduce((sum, job) => {
      if (job.startedAt && job.completedAt) {
        return sum + (job.completedAt.getTime() - job.startedAt.getTime());
      }
      return sum;
    }, 0);

    return totalTime > 0 ? (totalSize / totalTime) * 1000 : undefined; // Bytes per second
  }

  private checkDownloadCompletion(requestId: string): void {
    const jobs = this.downloadJobs.get(requestId);
    if (!jobs) return;

    const activeJobs = jobs.filter(job => 
      job.status === DownloadStatus.QUEUED ||
      job.status === DownloadStatus.SEARCHING ||
      job.status === DownloadStatus.DOWNLOADING ||
      job.status === DownloadStatus.RETRYING
    );

    if (activeJobs.length === 0) {
      // All jobs are complete
      this.emitCompleted(requestId);
      this.moveToHistory(requestId);
    } else {
      // Continue processing remaining jobs
      this.processDownloadQueue(requestId);
    }
  }

  private moveToHistory(requestId: string): void {
    const request = this.activeDownloads.get(requestId);
    const jobs = this.downloadJobs.get(requestId);

    if (!request || !jobs) return;

    const summary: DownloadSummary = {
      requestId,
      totalTracks: jobs.length,
      successfulTracks: jobs.filter(job => job.status === DownloadStatus.COMPLETED).length,
      failedTracks: jobs.filter(job => job.status === DownloadStatus.FAILED).length,
      skippedTracks: jobs.filter(job => job.status === DownloadStatus.CANCELLED).length,
      totalSize: jobs.reduce((sum, job) => sum + (job.fileSize || 0), 0),
      totalDuration: 0, // TODO: Calculate from track durations
      startedAt: request.createdAt,
      completedAt: new Date(),
      errors: jobs
        .filter(job => job.error)
        .map(job => ({
          trackId: job.track.id,
          trackName: `${job.track.artists.join(', ')} - ${job.track.name}`,
          error: job.error!
        }))
    };

    this.downloadHistory.unshift(summary);
    this.activeDownloads.delete(requestId);
    
    this.log(`Download ${requestId} completed: ${summary.successfulTracks}/${summary.totalTracks} tracks successful`);
  }

  private emitProgress(requestId: string): void {
    const jobs = this.downloadJobs.get(requestId);
    if (!jobs) return;

    const progress = this.calculateProgress(requestId, jobs);
    this.emit('download:progress', progress);
  }

  private emitJobUpdate(job: DownloadJob): void {
    this.emit('download:job-update', job);
    this.emitProgress(job.requestId);
  }

  private emitCompleted(requestId: string): void {
    const summary = this.downloadHistory.find(h => h.requestId === requestId);
    if (summary) {
      this.emit('download:completed', requestId, summary);
    }
  }
}
