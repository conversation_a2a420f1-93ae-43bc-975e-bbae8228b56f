// Core types for the SLDL Web GUI Backend

export interface Track {
  id: string;
  name: string;
  artists: string[];
  album: string;
  duration: string;
  uri?: string;
}

export interface PlaylistInfo {
  id: string;
  name: string;
  description: string;
  owner: string;
  trackCount: number;
  duration: string;
  isPublic: boolean;
  tracks: Track[];
}

export interface DownloadConfig {
  // Spotify credentials
  spotifyClientId: string;
  spotifyClientSecret: string;

  // Soulseek credentials
  soulseekUsername: string;
  soulseekPassword: string;

  // Download settings
  downloadDirectory: string;
  enableWav: boolean;
  enableAiff: boolean;
  enableMp3: boolean;
  minimumBitrate: number;
  concurrentDownloads: number;
  listenPort: number;
  enableYtdlpFallback: boolean;

  // SLDL Search Options
  sldlRemoveFt: boolean;
  sldlCustomRegex: string;
  sldlArtistMaybeWrong: boolean;
  sldlDesperate: boolean;
}

export interface DownloadRequest {
  id: string;
  playlistUrl: string;
  config: DownloadConfig;
  tracks: Track[];
  createdAt: Date;
}

export interface DownloadJob {
  id: string;
  requestId: string;
  track: Track;
  status: DownloadStatus;
  progress: number;
  error?: string;
  filePath?: string;
  fileSize?: number;
  startedAt?: Date;
  completedAt?: Date;
  retryCount: number;
  maxRetries: number;
}

export enum DownloadStatus {
  QUEUED = 'queued',
  SEARCHING = 'searching',
  DOWNLOADING = 'downloading',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  RETRYING = 'retrying',
  YTDLP_FALLBACK = 'ytdlp_fallback'
}

export interface DownloadProgress {
  requestId: string;
  totalTracks: number;
  completedTracks: number;
  failedTracks: number;
  currentTrack?: {
    name: string;
    artist: string;
    progress: number;
    status: DownloadStatus;
  };
  overallProgress: number;
  estimatedTimeRemaining?: number;
  downloadSpeed?: number;
}

export interface SLDLCliOptions {
  input: string;
  musicDir: string;
  username: string;
  password: string;
  concurrent: number;
  port: number;
  formats: string[];
  minBitrate: number;
  removeFt: boolean;
  customRegex?: string;
  artistMaybeWrong: boolean;
  desperate: boolean;
  ytDlp: boolean;
  maxRetries: number;
}

export interface SocketEvents {
  // Client to server
  'download:start': (request: DownloadRequest) => void;
  'download:pause': (requestId: string) => void;
  'download:resume': (requestId: string) => void;
  'download:cancel': (requestId: string) => void;
  'download:retry': (requestId: string, trackIds?: string[]) => void;

  // Server to client
  'download:progress': (progress: DownloadProgress) => void;
  'download:job-update': (job: DownloadJob) => void;
  'download:completed': (requestId: string, summary: DownloadSummary) => void;
  'download:error': (requestId: string, error: string) => void;
  'connection:status': (status: 'connected' | 'disconnected') => void;
}

export interface DownloadSummary {
  requestId: string;
  totalTracks: number;
  successfulTracks: number;
  failedTracks: number;
  skippedTracks: number;
  totalSize: number;
  totalDuration: number;
  startedAt: Date;
  completedAt: Date;
  errors: Array<{
    trackId: string;
    trackName: string;
    error: string;
  }>;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ServerConfig {
  port: number;
  host: string;
  frontendUrl: string;
  sldlCliPath: string;
  tempDir: string;
  maxConcurrentDownloads: number;
  maxRetries: number;
  timeoutMs: number;
}
